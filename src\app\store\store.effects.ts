import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from '@ngrx/effects';
import * as StoreActions from './store.actions';
import * as RigActions from './store.actions';
import { DataService } from '../services/data.service';
import { catchError, map, delay ,mergeMap, of, from, withLatestFrom, filter, switchMap, tap, EMPTY, forkJoin } from 'rxjs';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { UtilityService } from "../services/utility.service";
import { selectRigData, selectRigDataFromDb, selectRigLoadedFromDb, selectRigState, selectIsSyncing } from "./store.selector";
import { Store } from "@ngrx/store";
import { COMPANY_HEADER } from "src/models/COMPANY_HEADER";
import { CREW_HEADER } from "src/models/CREW_HEADER";
import { OPERATOR_HEADER } from "src/models/OPERATOR_HEADER";
import { UnviredCordovaSDK, RequestType } from "@awesome-cordova-plugins/unvired-cordova-sdk/ngx";
import { AppConstants } from '../constants/appConstants';
import { SynclogicService } from '../services/synclogic.service';
import { FORM_HEADER } from "src/models/FORM_HEADER";

@Injectable()
export class StoreEffects{

    constructor(private actions$: Actions, private unviredSdk: UnviredCordovaSDK, private dataService: DataService , private store: Store , private utilityService: UtilityService, private synclogicService: SynclogicService){}


    incrementAfterDelay$ = createEffect( () =>
       this.actions$.pipe(
            ofType(StoreActions.incrementAsync),
            delay(1000),
            map(() => StoreActions.increment())
        )
      )


      switchRig$ = createEffect(() =>
        this.actions$.pipe(
          ofType(RigActions.switchRig),
          mergeMap(({ rigId, deviceId }) =>
            from(this.dataService.getRigHeader(rigId, deviceId)).pipe(
              mergeMap((response) => {
                if (
                  response.InfoMessage &&
                  Array.isArray(response.InfoMessage) &&
                  response.InfoMessage.some((msg: any) => msg.category === "FAILURE")
                ) {
                  const errorMsg = response.InfoMessage.map((msg: any) => msg.message).join(', ');
                  return of(RigActions.switchRigFailure({ error: errorMsg }));
                }
      
                const rigHeader = response.RIG?.[0]?.RIG_HEADER;
      
                return from([
                  RigActions.switchRigSuccess({ rigData: rigHeader }),
                  RigActions.loadRigFromDb() // ✅ dispatch DB fetch
                ]);
              }),
              catchError(error => {
                console.error('API error', error);
                return of(RigActions.switchRigFailure({ error }));
              })
            )
          )
        )
      );
      
      


loadRigFromDb$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadRigFromDb),
    mergeMap(() =>
      from(this.dataService.getRigHeaderFromDB()).pipe(
        map((result) => {
          if (result) {
            console.log('Loaded rig data from DB:', result);
            return RigActions.loadRigFromDbSuccess({ rigData: result });
          } else {
            throw new Error('No rig data found in DB');
          }
        }),
        catchError(error => {
          console.error('[Effect] DB Load Error:', error);
          return of(RigActions.loadRigFromDbFailure({ error }));
        })
      )
    )
  )
);



loadAllTemplatesFromDb$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadAllTemplatesFromDb),
    withLatestFrom(this.store.select(selectRigData)), // Get rigData
    filter(([_, rigData]) => !!rigData && !!rigData.RIG_NO), // Ensure valid rigData
    switchMap(([_, rigData]) => {
      const company = rigData!.COMP_CODE;
      const rigNo = rigData!.RIG_NO;
      const rigType = rigData!.RIG_TYPE;
      const rigSubType = rigData!.RIG_SUB_TYPE;

      console.log('Loading templates from DB with:', { company, rigNo, rigType, rigSubType });

      return from(this.dataService.getAllTemplatesFromDB(company, rigNo, rigType, rigSubType)).pipe(
        map((result) => {
          console.log('✅ Loaded templates:', result);
          // Dispatch action to store the loaded templates
          return RigActions.loadAllTemplatesDbSuccess({ templates: result });
        }),
        catchError((error) => {
          console.error('[Effect] Template DB Load Error:', error);
          return of(RigActions.loadAllTemplatesDbFailure({ error }));
        })
      );
    })
  )
);



loadProgressBar$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadProgressBar),
    switchMap(() =>
      from(this.dataService.getProgressBarPercentage()).pipe( // 👈 convert Promise here
        map((percentage) =>
          RigActions.loadProgressBarSuccess({ percentage })
        ),
        catchError((error) =>
          of(RigActions.loadProgressBarFailure({ error }))
        )
      )
    )
  )
);


  loadPrefilledData$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.loadPrefilledData),
      withLatestFrom(this.store.select(selectRigState)),
      mergeMap(([action, rigState]) =>
        forkJoin({
          RIG_TYPE: of(rigState.rigData?.RIG_TYPE).pipe(
            catchError(err => {
              console.error('RIG_TYPE error:', err);
              return of(undefined);
            })
          ),
          RIG_SUB_TYPE: of(rigState.rigData?.RIG_SUB_TYPE).pipe(
            catchError(err => {
              console.error('RIG_SUB_TYPE error:', err);
              return of(undefined);
            })
          ),
          RIG_NO: of(rigState.rigData?.RIG_NO).pipe(
            catchError(err => {
              console.error('RIG_NO error:', err);
              return of(undefined);
            })
          ),
          COMP_CODE: of(rigState.rigData?.COMP_CODE).pipe(
            catchError(err => {
              console.error('COMP_CODE error:', err);
              return of(undefined);
            })
          ),
          TIME_ZONE: of(this.utilityService.getTimeZone()),



          COMPANY: from(this.dataService.getCompanyHeaderData()).pipe(
            map(companyData => {
              console.log('companyNameData', companyData)
              const companyNameData = companyData?.DESCR;
              console.log("companyNameData.data?.logoData is:", companyNameData);
              return companyNameData ?? undefined;
            })
          ),
          LOGO: from(this.dataService.getCompanyHeaderData()).pipe(
            map(companyData => {
              console.log('companyData', companyData)
              const logoData = companyData?.LOGO;
              console.log("companyData.data?.logoData is:", logoData);
              return logoData ?? undefined;
            })
          ),


          WORK_CENTER: from(this.dataService.getOperatorHeaderData()).pipe(
            map(operatorData => {
              const location = operatorData[0]?.LOCATION;
              return location ?? undefined;
            }),
            catchError(err => {
              console.error('Work center error:', err);
              return of(undefined);
            })
          ),
          USER_ID: from(this.unviredSdk.userSettings()).pipe(
            map(settings => settings.data?.USER_ID),
            catchError(err => {
              console.error('User data error:', err);
              return of('');
            })
          ),

          USER_NAME: from(this.unviredSdk.userSettings()).pipe(
            map(settings => (settings.data as any)?.FIRST_NAME.trim() + ' ' + (settings.data as any)?.LAST_NAME.trim() ),
            catchError(err => {
              console.error('User name error:', err);
              return of('');
            })
          ),
          OPERATOR: from(this.dataService.getOperatorHeaderData()).pipe(
            catchError(err => {
              console.error('OPERATOR error:', err);
              return of([]);
            })
          ),
          CREW: from(this.dataService.getCrewHeaderData()).pipe(
            catchError(err => {
              console.error('CREW error:', err);
              return of([]);
            })
          ),
          NOTIFICATION_TYPE: from(this.dataService.getNotifTypeHeaderData()).pipe(
            catchError(err => {
              console.error('NOTIFICATION_TYPE error:', err);
              return of([]);
            })
          ),
          NOTIFICATION_PRIORITY: from(this.dataService.getPriorityHeaderData()).pipe(
            catchError(err => {
              console.error('NOTIFICATION_PRIORITY error:', err);
              return of([]);
            })
          ),
          NOTIFICATION_CODE_GROUP: from(this.dataService.getCodeGroupHeaderData()).pipe(
            catchError(err => {
              console.error('NOTIFICATION_CODE_GROUP error:', err);
              return of([]);
            })
          ),
          NOTIFICATION_CODE: from(this.dataService.getCodeHeaderData()).pipe(
            catchError(err => {
              console.error('NOTIFICATION_CODE error:', err);
              return of([]);
            })
          )
        }).pipe(
          map(prefilledData => {
            console.log('[Effect] Fetched prefilledData:', prefilledData);
            return RigActions.loadPrefilledDataSuccess({ prefilledData });
          }),
          catchError(error => {
            console.error('[Effect] loadPrefilledData failed:', error);
            return of(RigActions.loadPrefilledDataFailure({ error }));
          })
        )
      )
    )
  );


 
  // SYNC effects (Rig Docs)
  loadGraphToken$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.loadGraphToken),
      switchMap(() =>
        from(this.unviredSdk.syncForeground(RequestType.QUERY as any, {}, '', AppConstants.PA_GET_GRAPH_API_TOKEN, false) as any).pipe(
          map((result: any) => {
            const token = typeof result?.data === 'string' ? result.data : result?.data?.token;
            if (!token) throw new Error('No token');
            return RigActions.loadGraphTokenSuccess({ token });
          }),
          catchError((error) => of(RigActions.loadGraphTokenFailure({ error })))
        )
      )
    )
  );

  loadSharepointSites$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.loadSharepointSites),
      switchMap(() =>
        from(this.unviredSdk.dbSelect(AppConstants.TABLE_SP_SITE_HEADER as any, {} as any)).pipe(
          map((res: any) => RigActions.loadSharepointSitesSuccess({ sites: res?.data || [] })),
          catchError((error) => of(RigActions.loadSharepointSitesFailure({ error })))
        )
      )
    )
  );

  loadSiteMeta$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.loadSiteMeta),
      switchMap(() =>
        from(this.unviredSdk.syncForeground(RequestType.QUERY as any, {}, '', AppConstants.PA_SP_SITE_META_GET, false) as any).pipe(
          map((res: any) => {
            const siteMetaArray = typeof res?.data === 'string' ? JSON.parse(res.data).SP_SITE_META : res?.data?.SP_SITE_META;
            return RigActions.loadSiteMetaSuccess({ siteMeta: Array.isArray(siteMetaArray) ? siteMetaArray : [] });
          }),
          catchError((error) => of(RigActions.loadSiteMetaFailure({ error })))
        )
      )
    )
  );

  // Poll for site headers after app is ready (start on Startup or initial data ready)
  ensureSiteHeaders$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.ensureSiteHeadersStart),
      switchMap(() =>
        // poll every 3s up to ~60s
        from((async () => {
          for (let i = 0; i < 20; i++) {
            try {
              const res: any = await this.unviredSdk.dbSelect(AppConstants.TABLE_SP_SITE_HEADER as any, {} as any);
              const sites = res?.data || [];
              if (Array.isArray(sites) && sites.length > 0) {
                // also dispatch a fresh metadata load so service observes a new emission
                this.store.dispatch(RigActions.loadSiteMeta());
                return RigActions.loadSharepointSitesSuccess({ sites });
              }
            } catch {}
            await new Promise(r => setTimeout(r, 3000));
          }
          return RigActions.loadSharepointSitesSuccess({ sites: [] });
        })())
      )
    )
  );

  updateDeviceSiteMeta$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.updateDeviceSiteMeta),
      switchMap(({ documents }) => {
        const lid = this.unviredSdk.guid();
        const updatePayload = { status: 'complete', timestamp: new Date().toISOString(), LID: lid, documents };
        return from(
          this.unviredSdk.syncBackground(RequestType.RQST as any, updatePayload, '', AppConstants.PA_SP_DEVICE_SITE_META_MODIFY, '', '', false) as any
        ).pipe(
          map(() => RigActions.updateDeviceSiteMetaSuccess()),
          catchError((error) => of(RigActions.updateDeviceSiteMetaFailure({ error })))
        );
      })
    )
  );

  checkServerSiteMeta$ = createEffect(() =>
    this.actions$.pipe(
      ofType(RigActions.checkServerSiteMeta),
      switchMap(() =>
        from(this.unviredSdk.syncForeground(RequestType.QUERY as any, {}, '', AppConstants.PA_SP_SITE_META_GET, false) as any).pipe(
          map((raw) => RigActions.checkServerSiteMetaSuccess({ raw })),
          catchError((error) => of(RigActions.checkServerSiteMetaFailure({ error })))
        )
      )
    )
  );

  // Auto-start sync when site headers arrive and not already syncing
  startSyncWhenHeadersLoaded$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(RigActions.loadSharepointSitesSuccess),
        withLatestFrom(this.store.select(selectIsSyncing)),
        filter(([{ sites }, isSyncing]) => Array.isArray(sites) && sites.length > 0 && !isSyncing),
        tap(() => {
          // Trigger service-driven sync
          try {
            console.log('[Effects] Auto-starting Rig Doc Sync because site headers are available');
            this.synclogicService.onRigDocSync(false);
          } catch {}
        })
      ),
    { dispatch: false }
  );


loadFormsFromDb$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.loadAllFormsFromDb),
    tap(() => console.log('[Effect] loadAllFormsFromDb triggered')),
    mergeMap(() =>
      from(this.dataService.getAllFormsFromTheDB()).pipe(
        tap(forms => console.log('[Effect] DB returned forms:', forms)),
        map(forms => RigActions.loadAllFormsDbSuccess({ forms })),
        catchError(error => {
          console.error('[Effect] DB load failed:', error);
          return of(RigActions.loadAllFormsDbFailure({ error }));
        })
      )
    )
  )
);

createForm$ = createEffect(() =>
  this.actions$.pipe(
    ofType(RigActions.createForm),
    tap(() => console.log('[Effect] createForm triggered')),
    mergeMap(({ template }) => {
      // console.log('template in createform effect is ' , template);
      return from(this.dataService.createFormAndSaveToDB(template)).pipe(
        tap(result => console.log('[Effect] Form created:', result)),
        map(result => RigActions.loadAllFormsFromDb()),
        catchError(error => {
          console.error('[Effect] Form creation failed:', error.error);
          return of(RigActions.loadAllFormsDbFailure({ error }));
        })
      );
    })
  )
);

}