Stack trace:
Frame         Function      Args
0007FFFF9450  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9450, 0007FFFF8350) msys-2.0.dll+0x1FEBA
0007FFFF9450  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9728) msys-2.0.dll+0x67F9
0007FFFF9450  000210046832 (000210285FF9, 0007FFFF9308, 0007FFFF9450, 000000000000) msys-2.0.dll+0x6832
0007FFFF9450  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9450  0002100690B4 (0007FFFF9460, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9730  00021006A49D (0007FFFF9460, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDD87B0000 ntdll.dll
7FFDD67A0000 KERNEL32.DLL
7FFDD5C60000 KERNELBASE.dll
7FFDD7300000 USER32.dll
7FFDD6370000 win32u.dll
7FFDD7F10000 GDI32.dll
7FFDD6020000 gdi32full.dll
7FFDD6260000 msvcp_win.dll
7FFDD6140000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDD8230000 advapi32.dll
7FFDD80B0000 msvcrt.dll
7FFDD66F0000 sechost.dll
7FFDD5C30000 bcrypt.dll
7FFDD65D0000 RPCRT4.dll
7FFDD5260000 CRYPTBASE.DLL
7FFDD63A0000 bcryptPrimitives.dll
7FFDD8160000 IMM32.DLL
