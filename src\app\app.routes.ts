import { Routes } from '@angular/router';

export const routes: Routes = [
  {
    path: 'ngrx-counter',
    loadComponent: () => import('./ngrx-counter/ngrx-counter.page').then((m) => m.ngrxCounter),
  },
  // {
  //   path: '',
  //   redirectTo: 'login',
  //   pathMatch: 'full',
  // },
  {
    path: 'login',
    loadComponent: () => import('./login/login.page').then( m => m.LoginPage)
  },
  {
    path: 'templates',
    loadComponent: () => import('./pages/templates/templates.page').then( m => m.TemplatesPage)
  },
  {
    path: 'forms',
    loadComponent: () => import('./pages/forms/forms.page').then( m => m.FormsPage)
  },
  {
    path: 'release-notes',
    loadComponent: () => import('./pages/release-notes/release-notes.page').then( m => m.ReleaseNotesPage)
  },
  {
    path: 'inbox',
    loadComponent: () => import('./pages/inbox/inbox.page').then( m => m.InboxPage)
  },
  {
    path: 'settings',
    loadComponent: () => import('./pages/settings/settings.page').then( m => m.SettingsPage)
  },
  {
    path: 'select-list',
    loadComponent: () => import('./pages/select-list/select-list.page').then( m => m.SelectListPage)
  },
  {
    path: 'topics',
    loadComponent: () => import('./pages/topics/topics.page').then( m => m.TopicsPage)
  },
  {
    path: 'stmr-options-popover',
    loadComponent: () => import('./pages/stmr-options-popover/stmr-options-popover.page').then( m => m.StmrOptionsPopoverPage)
  },
  {
    path: 'stmr-details',
    loadComponent: () =>
      import('./pages/stmr-details/stmr-details.page').then(m => m.STMRDetailsPage)
  },



];