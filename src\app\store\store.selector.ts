import { AppState, NotificationState, ProgressState, TemplateState , PrefilledState, FormsState } from './app.state';
import { CounterState } from './store.reducer';
import { createFeatureSelector, createSelector } from '@ngrx/store';
import { RigState  } from "./app.state";
import { SyncState } from './app.state';

export const selectCounterState = (state: AppState) => state.counter;

export const selectCount = createSelector(
  selectCounterState,
  (state: CounterState) => state.counter
);

export const selectRigState = createFeatureSelector<RigState>('rig');

export const selectRigData = createSelector(
  selectRigState,
  (state) => {
    console.log('selectRigData selector called, state:', state);
    return state.rigData;
  }
);


export const selectRigLoadedFromDb = createSelector(
  selectRigState,
  (state) => (state.loadedFromDb ? state.rigData : null)
);


export const selectRigLoadedFromServer = createSelector(
  selectRigState,
  (state) => state.loadedFromServer 
);

export const selectRigError = createSelector(
  selectRigState,
  (state) => state.error
);

export const selectRigDataFromDb = createSelector(
  selectRigLoadedFromDb, // this already checks that it's loaded
  selectRigData,
  (loaded, rigData) => (loaded ? rigData : null)
);

export const selectNotificationState = createFeatureSelector<NotificationState>('notification');

export const selectAllNotifications = createSelector(
  selectNotificationState,
  (state) => {
    console.log('Selector: selectAllNotifications ran');
    return state?.events ?? [];
  }
);

export const selectTemplateState = createFeatureSelector<TemplateState>('template');

export const selectAllTemplates = createSelector(
  selectTemplateState,
  (state: TemplateState) => state.templates
);



export const selectTemplatesLoadedFromDb = createSelector(
  selectTemplateState,
  (state) => state.loadedFromDb 
);



export const selectTemplateError = createSelector(
  selectTemplateState,
  (state) => state.error
);

export const selectProgressState = createFeatureSelector<ProgressState>('progress');

export const selectProgressPercentage = createSelector(
  selectProgressState,
  (state) => state.percentage
);

export const selectPrefilledState = createFeatureSelector<PrefilledState>('prefilled');
export const selectPrefilledData = createSelector(
  selectPrefilledState,
  (state) => state.prefilledData
);

export const selectSyncState = createFeatureSelector<SyncState>('sync');
export const selectIsSyncing = createSelector(selectSyncState, (state) => state.isSyncing);
export const selectSyncProgress = createSelector(selectSyncState, (state) => state.progress);
export const selectSyncCurrentFile = createSelector(selectSyncState, (state) => state.currentSyncFile);
export const selectGraphToken = createSelector(selectSyncState, (state) => state.graphToken);
export const selectSiteHeaders = createSelector(selectSyncState, (state) => state.siteHeaders);
export const selectSiteMeta = createSelector(selectSyncState, (state) => state.siteMeta);
export const selectSyncError = createSelector(selectSyncState, (state) => state.error);


export const selectFormsState = createFeatureSelector<FormsState>('forms');

export const selectAllForms = createSelector(
  selectFormsState,
  (state: FormsState) => {
    console.log('[Selector] selectAllForms called, state:', state);
    return state.forms;
  }
);

export const selectFormsLoading = createSelector(
  selectFormsState,
  (state) => state.loading
);

export const selectFormsError = createSelector(
  selectFormsState,
  (state) => state.error
);