.normal-case {
    text-transform: none !important;
  }


  .group-header {
    --background: #3a3a3a; // dark gray background
    color: white;
    font-weight: bold;
  }
  
  .group-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .count {
    font-weight: normal;
    color: #ccc; // optional: lighter color for count
  }

  .thumbnail-tight {
    margin-right: 0px; // Reduce spacing between thumbnail and label
  }


  .icon-wrapper {
    position: relative;
    width: 2.5rem;
    height: 2.5rem;
  
    .base-icon {
      font-size: 2.1rem;
      color: #444;
    }
  
    .star-icon,
    .wifi-icon {
      position: absolute;
      font-size: 0.9rem;
      color: orange;
    }
  
    .star-icon {
      top: -0.3rem;
      right: -0.3rem;
      color: orange;
    }
  
    .wifi-icon {
      top: -0.3rem;
      left: -0.8rem;
      color: orange;
    }

  }


  .download-status-container {
    padding: 16px;
    text-align: center;
  }
  
  .download-label {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #000;
  }
  
  .custom-progress-bar {
    --progress-background: #e0e0e0;
    --background: #e0e0e0;
    --buffer-background: #e0e0e0;
    --progress-bar-background: #4caf50; // green
    border-radius: 8px;
    height: 20px;
    margin: 0 auto;
    width: 90%;
  }
  

  .progress-container {
    transition: opacity 0.3s ease-in-out;
  }
  