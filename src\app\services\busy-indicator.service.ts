import { Injectable } from '@angular/core';
import { <PERSON>ert<PERSON><PERSON>roller, LoadingController, ToastController } from '@ionic/angular/standalone';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class BusyIndicatorService {


    isLoading = false;
  message = '';
    loading: any;
 showAlert: any = null;
  constructor( private toastController: ToastController,  public alertController: AlertController,private loadingController: LoadingController,  private translate: TranslateService,) { }


    async showBusyIndicator(msg: string, indType: string) {
    let loader = null;
    this.isLoading = true;
    this.message = msg
    if (indType == 'dots') {
      loader = await this.loadingController.create({
        message: msg,
        spinner: 'dots',
        animated: true,
        showBackdrop: true,
        translucent: true
      });
    } else {
      loader = await this.loadingController.create({
        message: msg,
        spinner: 'crescent',
        animated: true,
        showBackdrop: true,
        translucent: true
      });
    }
    await loader.present();
    if (!this.isLoading) {
      this.loadingController.dismiss();
    }
  }

   async displayBusyIndicator(message: string) {
    this.loading = await this.loadingController.create({ message });
    await this.loading.present();
  }

  async dismissBusyIndicator() {
    this.isLoading = false;
    return await this.loadingController.dismiss();
  }
    hideBusyIndicator() {
    if (this.loading) {
      this.loading.dismiss();
      this.loading = undefined;
    }
  }
  

    async presentAlert(errorResponse: string) {
    this.showAlert = await this.alertController.create({
      header: this.translate.instant('Error'),
      message: errorResponse,
      animated: true,
      backdropDismiss: false,
      buttons: [
        {
          text: this.translate.instant('OK'),
        },
      ],
    });
    await this.showAlert.present();
  }


  async presentToast(message: string) {
    const toast = await this.toastController.create({
      message: message,
      duration: 5000,
      position: 'bottom',
    });

    await toast.present();
  }

}

