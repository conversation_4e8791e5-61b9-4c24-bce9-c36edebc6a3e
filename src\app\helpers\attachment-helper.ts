import { Injectable } from '@angular/core';
import { Platform } from '@ionic/angular';

// Utility to safely detect Electron
export function isElectron(): boolean {
  try {
    const userAgent = navigator.userAgent || '';
    const hasElectronProcess = !!(window as any).process?.versions?.electron;
    const hasElectronInUserAgent = userAgent.toLowerCase().includes('electron');
    const result = hasElectronProcess || hasElectronInUserAgent;
    return result;
  } catch (error) {
    console.warn('Error detecting Electron:', error);
    return false;
  }
}

// Get base path depending on platform
export function getBasePath(): string {
  if (isElectron()) {
    console.log('getBasePath: Detected Electron platform');
    return '';
  }

  // Cordova/Ionic Native File plugin
  if ((window as any).cordova?.file?.documentsDirectory) {
    // console.log('getBasePath: Detected Cordova platform');

    const base = (window as any).cordova.file.documentsDirectory;
    // console.log('getBasePath: Mobile base path:', base);
    return base
  }

  // Fallback (browser, etc)
  console.log('getBasePath: Fallback to default path');
  return '/tmp/RigDocs/';
}

/**
 * Normalizes a local path for the current platform
 * For Electron: returns the path as-is (supports absolute paths)
 * For iOS/Android: strips drive letter and root folder, creates proper structure
 */
export function normalizeLocalPath(localPath: string): string {
  // Input validation
  if (!localPath || typeof localPath !== 'string') {
    console.warn('normalizeLocalPath: Invalid input:', localPath);
    return '';
  }

  if (isElectron()) {
    // For Electron, return the path as-is, just normalize slashes
    const normalized = localPath.replace(/\\/g, '/');
    return normalized;
  } else {
    // For mobile platforms, strip drive letter and root folder
    const normalizedPath = localPath.replace(/\\/g, '/');

    // Remove drive letter if present (e.g., "C:/")
    let pathWithoutDrive = normalizedPath.replace(/^[a-zA-Z]:\/?/, '');

    // Remove leading slashes
    pathWithoutDrive = pathWithoutDrive.replace(/^\/+/, '');

    // Split by path separator and find the root folder (usually "RigDocs" or "Documents")
    const pathParts = pathWithoutDrive.split('/');
    const rootFolderIndex = pathParts.findIndex(part =>
      part.toLowerCase() === 'rigdocs' ||
      part.toLowerCase() === 'documents'
    );

    if (rootFolderIndex >= 0) {
      // Return everything after the root folder
      const result = pathParts.slice(rootFolderIndex).join('/');
      // console.log('normalizeLocalPath (Mobile):', localPath, '->', result);
      return result;
    }

    // If no root folder found, return the path as-is
    console.log('normalizeLocalPath (Mobile, no root folder):', localPath, '->', pathWithoutDrive);
    return pathWithoutDrive;
  }
}

@Injectable({ providedIn: 'root' })
export class AttachmentHelper {
  constructor(private platform: Platform) { }

  /**
   * Ensures a directory exists. If not, creates it recursively.
   * If no path is specified, uses getBasePath().
   */
  async ensureDirectory(folderPath?: string): Promise<string> {
    await this.platform.ready();
    let path = folderPath && folderPath.trim() ? folderPath : getBasePath();
    await this.createDirectoriesRecursively(path);
    return path;
  }

  // Read file as ArrayBuffer
  async readExternalFile(filePath: string): Promise<ArrayBuffer> {
    await this.platform.ready();
    return new Promise((resolve, reject) => {
      (window as any).resolveLocalFileSystemURL(filePath,
        (fileEntry: any) => {
          // File exists, proceed to read
          fileEntry.file((file: any) => {
            const reader = new FileReader();
            reader.onloadend = function () {
              resolve(this.result as ArrayBuffer);
            };
            reader.onerror = function (err) {
              console.error(filePath, "readExternalFile", "Read error: " + err);
              reject(err);
            };
            reader.readAsArrayBuffer(file);
          }, (error: any) => {
            // Handle error reading file
            console.error(filePath, "readExternalFile", "Error reading file: " + error);
            reject(error);
          });
        },
        (error: any) => {
          // File does not exist
          // console.warn('File does not exist:', filePath);
          // Handle missing file (e.g., skip, download, etc.)
          reject(error);
        }
      );
    });
  }

  // Write file (create/update)
  async writeExternalFile(fullFilePath: string, data: ArrayBuffer): Promise<void> {
    await this.platform.ready();

    return new Promise((resolve, reject) => {
      const folderPath = fullFilePath.substring(0, fullFilePath.lastIndexOf('/'));
      const fileName = fullFilePath.substring(fullFilePath.lastIndexOf('/') + 1);

      (window as any).resolveLocalFileSystemURL(folderPath, (dirEntry: any) => {
        dirEntry.getFile(fileName, { create: true }, (fileEntry: any) => {
          fileEntry.createWriter((fileWriter: any) => {
               fileWriter.onwriteend = () => {
            // console.log(`[AttachmentHelper] Successfully wrote file: ${fullFilePath}`);
            resolve();
          };
             fileWriter.onerror = (e: any) => {
            console.error(`[AttachmentHelper] Error writing file: ${fullFilePath}`, e);
            reject(e);
          };
            fileWriter.write(new Blob([data]));
          }, reject);
        }, reject);
      }, reject);
    });
  }

  async deleteExternalFile(filePath: string): Promise<void> {
    await this.platform.ready();
    return new Promise((resolve, reject) => {
      (window as any).resolveLocalFileSystemURL(filePath,
        (fileEntry: any) => fileEntry.remove(resolve, reject),
        reject
      );
    });
  }

  /**
   * Creates directories recursively.
   * 
   * - For Electron: supports absolute path from drive root.
   * - For Cordova/iOS/Android: starts from basePath and creates nested dirs.
   */
  async createDirectoriesRecursively(dirPath: string): Promise<string> {
    await this.platform.ready();

    if (isElectron()) {
      // For Electron: allow absolute paths traversing from drive letter
      return new Promise((resolve, reject) => {
        const pathParts = dirPath.replace(/\\/g, '/').split('/').filter(p => p.length > 0);
        let currentPath = '';
        if (pathParts.length > 0 && /^[a-zA-Z]:$/.test(pathParts[0])) {
          currentPath = pathParts[0] + '/';
          pathParts.shift();
        }
        const createNextDirectory = (index: number) => {
          if (index >= pathParts.length) return resolve(dirPath.endsWith('/') ? dirPath : dirPath + '/');
          const part = pathParts[index].replace(/[<>:"/\\|?*\x00-\x1F]/g, '').trim();
          currentPath += part + '/';
          (window as any).resolveLocalFileSystemURL(
            currentPath,
            () => createNextDirectory(index + 1), // exists
            () => { // doesn't exist, create
              const parent = currentPath.replace(/\/$/, '').substring(0, currentPath.replace(/\/$/, '').lastIndexOf('/')) + '/';
              (window as any).resolveLocalFileSystemURL(parent,
                (parentDir: any) => {
                  parentDir.getDirectory(part, { create: true },
                    () => createNextDirectory(index + 1),
                    reject
                  );
                },
                reject
              );
            }
          );
        };
        createNextDirectory(0);
      });
    } else {
      // For Cordova (mobile): always create dir relative to base path
      return new Promise((resolve, reject) => {
        const basePath = getBasePath().replace(/\\/g, '/').replace(/\/+$/, '') + '/';
        const fullPath = dirPath.replace(/\\/g, '/').replace(/\/+$/, '');
        const baseParts = basePath.split('/').filter(p => p.length > 0);
        const pathParts = fullPath.split('/').filter(p => p.length > 0);

        // Only create dirs relative to base path
        const toCreate = pathParts.slice(baseParts.length);

        if (!toCreate.length) return resolve(basePath);

        (window as any).resolveLocalFileSystemURL(basePath,
          (rootDir: any) => {
            let currentDir = rootDir, idx = 0;
            const createNext = () => {
              if (idx >= toCreate.length)
                return resolve(basePath + toCreate.join('/') + '/');
               const safe = decodeURIComponent(
            toCreate[idx++].replace(/[<>:"/\\|?*\x00-\x1F]/g, '').trim()
          );
              currentDir.getDirectory(safe, { create: true },
                (newDir: any) => {
                  currentDir = newDir;
                  createNext();
                },
                (err: any) => reject(err)
              );
            };
            createNext();
          },
          (err: any) => reject(err)
        );
      });


    }
  }
}
