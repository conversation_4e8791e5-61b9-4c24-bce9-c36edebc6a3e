import { Injectable, Ng<PERSON>one } from '@angular/core';
import { NotificationListenerType, NotifResult, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { addNotification, loadProgressBar  } from './store/store.actions';
import { Store } from '@ngrx/store';
import { DataService } from './services/data.service';
import { selectProgressPercentage } from './store/store.selector';
import { filter, Observable, take } from 'rxjs';
import  * as RigActions from './store/store.actions';



@Injectable({
  providedIn: 'root'
})
export class NotificationService {

  jsonData: any;
  progress$!: Observable<any>;
  
  constructor(private unviredSdk: UnviredCordovaSDK , private ngZone: NgZone , private store : Store , private dataService: DataService) { }


  init() {
    this.unviredSdk.registerNotifListener().subscribe((result: NotifResult) => {
      this.ngZone.run(() => {
        console.log('Notification result:', result);
  
        this.store.dispatch(addNotification({ notification: result }));
  
        switch (result.type) {
          case NotificationListenerType.dataReceived:
            console.log('Data received:', result.data);
            break;
          case NotificationListenerType.dataSend:
            console.log('Data sent:', result.data);
            break;
          case NotificationListenerType.dataChanged:
            console.log('Data changed:', result.data);
            break;
          case NotificationListenerType.JWTTokenReceived:
            console.log('JWT Token received:', result.data);
            break;
          case NotificationListenerType.attachmentDownloadSuccess:
          this.store.dispatch(loadProgressBar());
          break;
          case NotificationListenerType.incomingDataProcessingFinished:
            this.dataService.getPrefillData().subscribe(data => {
              this.jsonData = data;
              console.log('JSON loaded:', data);
            });

            this.store.dispatch(RigActions.loadPrefilledData());
            this.store.dispatch(RigActions.loadAllFormsFromDb());


        }
      });
    });
  }
  

}
