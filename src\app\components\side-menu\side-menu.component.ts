import { Component, CUSTOM_ELEMENTS_SCHEMA, OnInit } from '@angular/core';
import { RouterModule, Router } from '@angular/router';
import { ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { IonMenu, IonMenuToggle, IonHeader, IonToolbar, IonTitle, IonContent, IonList, IonItem, AlertController, PopoverController, MenuController, IonCardContent, Platform } from '@ionic/angular/standalone';
import { AppConstants } from 'src/app/constants/appConstants';
import { ModalController } from '@ionic/angular';
import { SiteRigModalComponent } from 'src/app/components/site-rig-modal/site-rig-modal.component';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { filter, Observable, take } from 'rxjs';
import { selectAllForms, selectAllTemplates, selectPrefilledData, selectRigLoadedFromDb } from 'src/app/store/store.selector';
import { select, Store } from '@ngrx/store';
import { AppState } from 'src/app/store/app.state';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import  * as RigActions from 'src/app/store/store.actions';
import { DataService } from 'src/app/services/data.service';
import { FORM_HEADER } from 'src/models/FORM_HEADER';
@Component({
  selector: 'app-side-menu',
  templateUrl: './side-menu.component.html',
  styleUrls: ['./side-menu.component.scss'],
  imports: [RouterModule, CommonModule, FormsModule, IonMenu, IonHeader, IonToolbar, IonTitle, IonContent, IonList, IonItem, IonMenuToggle, IonCardContent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [ModalController]
})
export class SideMenuComponent  implements OnInit {

   serverName: string | null = null;
    rigData$!: Observable<RIG_HEADER| null>;
    modalRef: HTMLIonPopoverElement | null = null;
    prefilledData$!: Observable<any>
    templates$!: Observable<TEMPLATE_HEADER[]>
    forms$!: Observable<FORM_HEADER[]>
    version = AppConstants.RELEASE_NUMBER + '' + AppConstants.RELEASE_DATE
    // version = '1.0.149 - 2025-07-30_16-57-35'
  constructor(
    public dataService: DataService,
    private modalController:ModalController,
    private unviredSDK: UnviredCordovaSDK ,
    private menu: MenuController ,
    private platform: Platform,
    private popoverController: PopoverController,
    private store: Store<AppState>,
    private router: Router
  )  { 
    this.rigData$ = this.store.select(selectRigLoadedFromDb); 
  }

  ngOnInit() {
      this.prefilledData$ = this.store.select(selectPrefilledData);
       this.prefilledData$
    .pipe(
      filter((data): data is { LOGO: string } => !!data && !!data.LOGO)
    )
    .subscribe(data => {
      console.log('logo is in ngoninit', data.LOGO);
    });
   this.templates$ = this.store.select(selectAllTemplates);
    this.forms$ = this.store.select(selectAllForms)
  }


ionViewWillEnter() {
  this.rigData$
    .pipe(
      filter(rig => !!rig?.RIG_NO),
      take(1)
    )
    .subscribe(() => this.modalController.dismiss());

  

}
   closeMenu() {
    console.log('close menu')
    this.menu.close();
  }

  navigateTo(path: string) {

    console.log('the path is ' , path)
    this.menu.close();
    this.router.navigate([path]);
  }

async openSiteNumberPopup() {
  console.log('openSiteNumberPopup called from side menu');
  const modal = await this.popoverController.create({
    component: SiteRigModalComponent,
    cssClass: 'custom-site-modal',
    backdropDismiss: true,
    mode: 'md'
  });

  modal.onDidDismiss().then((result: any) => {
    if (result) {
      console.log('Site number submitted:', result);
      // this.saveSiteNumber(result.data);
    }
  });

  await modal.present();
}


 async openReports() {
  console.log('openReports() called');
  console.log('typeof window.ump:', typeof (window as any).ump);
console.log('typeof window.ump.userSettings:', typeof (window as any).ump?.userSettings);
 
  let umpUrl = '';
  let token = '';
 
  try {
    if ((window as any).ump && typeof (window as any).ump.userSettings === 'function') {
      const userSettings = await new Promise<any>((resolve, reject) => {
        (window as any).ump.userSettings(
          (result: any) => resolve(result?.data),
          (err: any) => reject(err)
        );
      });
      umpUrl = userSettings?.SERVER_URL || '';
      token = userSettings?.JWT_TOKEN || userSettings?.loginToken || '';
      console.log('userSettings SERVER_URL:', userSettings?.SERVER_URL);
      console.log('userSettings JWT_TOKEN:', userSettings?.JWT_TOKEN);
      console.log('Full userSettings:', userSettings);
    } else {
      umpUrl = localStorage.getItem('ump_url') || '';
      token = localStorage.getItem('login_token') || '';
    }
  } catch (e) {
    console.error('Error reading userSettings:', e);
    umpUrl = '';
    token = '';
  }
 
  if (!umpUrl || !token) {
    alert('Missing UMP URL or login token');
    // this.router.navigate(['/login']);
    return;
  }
 
  const baseUrl = umpUrl.replace(/\/+$/, '');
  const formsUrl = baseUrl.replace(/\/UMP$/, '/FORMS');
  const url = `${formsUrl}/?action=login&token=${encodeURIComponent(token)}`;
 
  // Open in external browser
    if (this.platform.is('electron')) {
      if (window.electronAPI && window.electronAPI.openExternal) {
        window.electronAPI.openExternal(url);
      } else {
        console.error('Electron API not available');
      }
    } else {
     
      window.location.href = url;
    console.log(window.location.href)
    }
  }

}


