/* filepath: c:\Users\<USER>\pd-forms-app-latest-ionic\src\app\pages\topics\topics.page.scss */
.custom-content {
  --background: #fff;
  padding: 0;
}

.section-header {
  background: #4a4a4a;
  color: #fff;
  font-size: 20px;
  padding: 12px 16px;
  font-weight: 500;
  margin-bottom: 0;
}

.section-body {
  background: #fff;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 18px;
}

.col-6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding-right: 16px;
}

.col-12 {
  flex: 0 0 100%;
  max-width: 100%;
}

.field-label {
  font-size: 16px;
  color: #222;
  font-weight: 500;
  margin-bottom: 6px;
  display: block;
}

.required {
  color: #d32f2f;
  font-weight: bold;
}

.select-link {
  color: #d32f2f;
  font-weight: 500;
  cursor: pointer;
  margin-left: 8px;
  font-size: 16px;
}

.date-row {
  margin-bottom: 0;
}

.date-time-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-gap: 8px;
  align-items: center;
  margin: 12px 0 0 0;
}

.date-time-labels,
.date-time-values,
.date-time-controls {
  display: contents;
}

.date-time-labels span,
.date-time-values span {
  text-align: center;
  font-size: 15px;
  color: #222;
  font-weight: 500;
}

.date-time-labels span {
  font-style: italic;
  color: #888;
}

.date-time-values span {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.date-time-section {
  width: 100%;
  margin-top: 10px;
}

.date-time-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 2px;
}

.date-time-cell {
  flex: 0 0 12.5%;
  text-align: center;
}

.date-time-labels .date-time-cell,
.date-time-values .date-time-cell {
  border-bottom: 1px solid #b4b4b4;
}

.exp-header {
  font-size: 13px;
  color: #888;
  font-style: italic;
}

.dt-btn {
  background: #6ec6ff;
  color: #222;
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 28px;
  font-size: 18px;
  margin: 2px auto;
  display: block;
  cursor: pointer;
}

.max-topic-time {
  margin-top: 8px;
  font-size: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.max-btn {
  background: #1976d2;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 4px 12px;
  font-size: 13px;
  margin-top: 4px;
  cursor: pointer;
}

.date-time-max {
  text-align: left;
  font-size: 14px;
  padding-left: 10px;
}

.date-time-max-btn {
  text-align: left;
  padding-left: 10px;
}

.date-time-error {
  color: #d32f2f;
  font-size: 13px;
  margin-left: 10px;
  text-align: left;
}

.desc-label {
  color: #d32f2f;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
  display: block;
}

.desc-textarea {
  width: 100%;
  min-height: 80px;
  border: 2px solid #888;
  border-radius: 2px;
  font-size: 16px;
  padding: 8px;
  color: #222;
  background: #fff;
  resize: vertical;
}

.forms-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 48px;
}

.no-forms {
  color: #888;
  font-size: 16px;
  font-style: italic;
}

.add-form-btn {
  background: none;
  border: none;
  color: #222;
  font-size: 28px;
  cursor: pointer;
  margin-right: 8px;
  padding: 0;
}

.save-btn {
  color: #fff;
  font-weight: bold;
  font-size: 16px;
  letter-spacing: 1px;
}