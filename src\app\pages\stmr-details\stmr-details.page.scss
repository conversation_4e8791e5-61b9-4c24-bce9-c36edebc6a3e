.section-header {
  background: #4a4646;
  color: #fff;
  font-weight: bold;
  font-size: 1.05rem;
  margin: 16px 0 0 0;
  border-radius: 0;
  width: 100%;
  box-sizing: border-box;
  letter-spacing: 0.5px;
  padding: 0; // Remove extra padding
}

.red-label, .required-label {
  color: #d32f2f !important;
  font-weight: bold;
  font-size: 1rem;
}

.red-underline {
  border-bottom: 2px solid #d32f2f !important;
  border-radius: 0;
}

.empty-state {
  color: #d32f2f;
  font-weight: bold;
  font-size: 1rem;
  padding: 8px 0;
}

ion-card {
  margin: 0 0 16px 0;
  box-shadow: none;
  border-radius: 0;
  border: none;
}
ion-card, ion-card-content {
  width: 100%;
  box-sizing: border-box;
}
ion-card-content {
  padding: 16px 12px 8px 12px;
}

ion-item {
  --padding-start: 8px;
  --padding-end: 8px;
}

ion-label {
  font-size: 1rem;
  font-weight: 500;
  color: #222;
}

ion-input, ion-input .native-input, input {
//   --highlight-color-focused: #000 !important;
  --highlight-color: #000 !important;
  --border-color: #000 !important;
  caret-color: #000 !important;
  color: #222;
}

ion-input {
  border-bottom: 1.5px solid #000 !important;
  border-radius: 0 !important;
}

// ion-input:focus, ion-input[focused] {
//   --highlight-color-focused: #000 !important;
//   border-bottom: 1.5px solid #000 !important;
// }

ion-input, .value-display {
  font-size: 1rem;
  color: #222;
  padding: 4px 0;
}
.value-display {
  border-bottom: 1px solid #222;
  min-height: 32px;
  display: flex;
  align-items: center;
}

ion-title {
  font-size: 1.25rem;
  font-weight: bold;
}
ion-buttons {
  align-items: center;
  color: white;
}
ion-toolbar {
  --min-height: 60px;
  --max-height: 60px;
  color: white;
  font-size: 1rem;
  font-weight: 600;
  padding: 0 16px;
  display: flex;
  align-items: center;
  border-radius: 8px 8px 0 0;
}

.section-toolbar {
  min-height: 50px;
  height: 42px;
  --min-height: 50px;
  --background: #4a4646 !important;
  --color: #fff;
  padding-left: 0;
  padding-right: 0;
  border-radius: 0;
  box-shadow: none;
  display: flex;
  align-items: center;
}

.section-title {
  text-align: left;
  font-size: 1.05rem;
  font-weight: 500;
  margin-left: 12px;
  color: #fff;
}

ion-button[fill="clear"] {
  --background: transparent;
  --color: #fff;
  min-width: 32px;
}

ion-button[disabled] {
  opacity: 0.5;
}

ion-icon {
  font-size: 1.2rem;
}

ion-radio-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;
}

ion-radio {
  margin-right: 4px;
}

@media (max-width: 768px) {
  .section-header {
    font-size: 1rem;
    padding: 8px 8px;
  }
  ion-card-content {
    padding: 8px 4px 4px 4px;
  }
  ion-label, .red-label, .required-label, .empty-state {
    font-size: 0.95rem;
  }
  ion-title {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .section-header {
    font-size: 0.95rem;
    padding: 8px 4px;
  }
  ion-card, ion-card-content {
    padding: 0 !important;
  }
  ion-item {
    --padding-start: 2px;
    --padding-end: 2px;
    font-size: 0.9rem;
  }
}

ion-content {
  --padding-start: 10px;
  --padding-end: 10px;
  // If you want top/bottom padding as well, add:
  // --padding-top: 16px;
  // --padding-bottom: 16px;
}