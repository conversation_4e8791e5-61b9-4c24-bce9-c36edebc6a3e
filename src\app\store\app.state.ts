import { RIG_HEADER } from "src/models/RIG_HEADER";
import { CounterState } from "./store.reducer";
import { NotifResult } from "@awesome-cordova-plugins/unvired-cordova-sdk/ngx";
import  { TEMPLATE_HEADER } from "src/models/TEMPLATE_HEADER";
import { COMPANY_HEADER } from "src/models/COMPANY_HEADER";
import { OPERATOR_HEADER } from "src/models/OPERATOR_HEADER";
import { CREW_HEADER } from "src/models/CREW_HEADER";
import { C_NOTIF_TYPE_HEADER } from "src/models/C_NOTIF_TYPE_HEADER";
import { C_PRIORITY_HEADER } from "src/models/C_PRIORITY_HEADER";
import { C_CODE_GROUP_HEADER } from "src/models/C_CODE_GROUP_HEADER";
import { C_CODE_HEADER } from "src/models/C_CODE_HEADER";
import { SP_SITE_HEADER } from "src/models/SP_SITE_HEADER";
import { FORM_HEADER } from "src/models/FORM_HEADER";
export interface AppState {
  counter: CounterState;
  notification: NotificationState;
  rig: RigState;
  template: TemplateState;
  progress: ProgressState;
  prefilled: PrefilledState;
  sync: SyncState;
  forms: FormsState
}

export interface RigState {
  rigData: RIG_HEADER | null;
  error: any;
  loadedFromDb: boolean;
  loadedFromServer: boolean; 
}

export interface NotificationState {
  events: NotifResult[];
}

export interface TemplateState {
  templates: TEMPLATE_HEADER[];
  error: any;
  loadedFromDb: boolean;
  loadedFromServer: boolean; 
} 

export interface ProgressState {
  percentage: number;
  error: any;
}


export interface PrefilledState {
  prefilledData: {
    'RIG_TYPE': string | undefined;
    'RIG_SUB_TYPE': string | undefined;
    'RIG_NO': string | undefined;
    'COMP_CODE': string | undefined;
    "USER_ID": string | undefined,
    "USER_NAME": string | undefined,
    "WORK_CENTER": string | undefined,
    'TIME_ZONE': string | undefined,
    'COMPANY': string | undefined,
    'LOGO': string | undefined,
    'OPERATOR': OPERATOR_HEADER[],
    'CREW': CREW_HEADER[],
    'NOTIFICATION_TYPE': C_NOTIF_TYPE_HEADER[],
    'NOTIFICATION_PRIORITY': C_PRIORITY_HEADER[],
    'NOTIFICATION_CODE_GROUP': C_CODE_GROUP_HEADER[],
    'NOTIFICATION_CODE': C_CODE_HEADER[]

  } | null;
}


export interface SyncState {
  isSyncing: boolean;
  progress: number;
  currentSyncFile: string;
  graphToken: string | null;
  siteHeaders: SP_SITE_HEADER[];
  siteMeta: any[];
  baseLocalPath: string | null;
  error: any | null;
}


export interface FormsState {
  forms: FORM_HEADER[];
  loading: boolean;
  error: any;
}






