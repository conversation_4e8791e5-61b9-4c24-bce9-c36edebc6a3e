# PD-Forms-App-Latest-Ionic

# 1. <PERSON><PERSON> the repository
git clone <repository-url>
cd <project-folder>

# 2. Create a new branch (replace 'my-branch' with your branch name)
git checkout -b my-branch

# 3. Push the new branch to remote
git push origin my-branch

# 4. Install dependencies
npm install

# 5. Add Electron platform 

ionic cordova platform add electron

# 6. Run the project in Electron

ionic cordova run electron


## OR for development with live reload:

ionic cordova run electron --livereload
-