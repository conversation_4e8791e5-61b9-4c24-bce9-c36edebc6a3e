<ion-header>
  <ion-toolbar color="primary" class="custom-toolbar">
    <ion-buttons slot="start">
      <ion-menu-button autoHide="false" class="white-icon"></ion-menu-button>
    </ion-buttons>
    <ion-title class="left-title">Templates</ion-title>

    <!-- <ion-button (click)="refreshData()" slot="end">Reload</ion-button> -->
  </ion-toolbar>

  <ion-toolbar color="primary" class="custom-toolbar">
    <ion-searchbar  color="light" placeholder="Filter" (ionInput)="onSearchChange($event.detail.value)"></ion-searchbar>
    
    </ion-toolbar>
    <ion-button expand="full" color="secondary" class="normal-case" (click)="createSTMR()"> Tap here to start Safety Meeting (STMR)</ion-button>

    <ng-container *ngIf="progress$ | async as progress">
   
      <div *ngIf="round(progress) < 100" class="progress-container">
      <p style="font-weight: bold; font-size: 16px; margin-top: 8px; text-align: center; color: black;">Template Download Status ({{ progress | number:'1.0-0' }}%)</p>
    <ion-progress-bar [value]="progress / 100" color="success" style="width: 100%; height: 6px; margin: 0 auto;"></ion-progress-bar>
        </div>
  </ng-container>

</ion-header>

<ion-content >


        <!-- Show Skeleton Loader while templates are loading -->
         <div  *ngIf="isLoading">
          <ion-item *ngFor="let item of skeletonArray">
            <ion-thumbnail slot="start">
              <ion-skeleton-text [animated]="true"></ion-skeleton-text>
            </ion-thumbnail>
            <ion-label>
              <h3>
                <ion-skeleton-text [animated]="true" style="width: 80%;"></ion-skeleton-text>
              </h3>
              <p>
                <ion-skeleton-text [animated]="true" style="width: 60%;"></ion-skeleton-text>
              </p>
              <p>
                <ion-skeleton-text [animated]="true" style="width: 30%;"></ion-skeleton-text>
              </p>
            </ion-label>
          </ion-item>
  
         </div>
      
<!-- Show templates once loaded and rigData is available -->
<ng-container *ngIf="rigData$ | async as rigData">
  <ng-container *ngIf="rigData.RIG_NO && !(isLoading)">
    <ng-container *ngIf="groupedTemplates$ | async as groupedTemplates">
      <ion-list *ngFor="let group of groupedTemplates">
        <ion-item-divider class="group-header">
          <ion-label>
            <div class="group-header-content">
              <span>{{ group.category }}</span>
              <span style="margin-left: 5px; margin-bottom: 2px;">({{ group.items.length }})</span>
            </div>
          </ion-label>
        </ion-item-divider>

        <ion-item *ngFor="let template of group.items">
          <ion-thumbnail slot="start" class="thumbnail-tight">
            <div class="icon-wrapper">
              <i class="fas fa-file-alt  base-icon"></i>
              <i class="fas fa-star star-icon" *ngIf="template.L_READ_FLAG == 'X'"></i>
              <i class="fas fa-wifi wifi-icon" *ngIf="template.IS_DATA_ENH == 'true'"></i>
            </div>
          </ion-thumbnail>
          
          <ion-label>
            <h2>{{ template.DESCR }}</h2>
            <p>
              Version: {{ template.L_VER_NO }} •
              Published: {{ template.PBLSH_ON | customDate }}
            </p>
          </ion-label>

          <ion-buttons slot="end">
            <ion-button
              *ngIf="template.ATT_STATUS === '' || template.ATT_STATUS === 0 || template.ATT_STATUS === 'DEFAULT'"
              color="medium"
              shape="round"
              class="btn-status btn-o">
              {{ 'Initial' | translate }}
            </ion-button>
          
            <ion-button color="primary" fill="solid" class="normal-case"
              *ngIf="(template.ATT_STATUS === 2 || template.ATT_STATUS === 'DOWNLOADED') && (template.TMPLT_ID !== 'STMR' && template.TMPLT_ID !== 'CTA')"
              (click)="createForm($event, template)"
              shape="round">
              {{ 'Create Form' | translate }}
            </ion-button>
          
            <ion-button
              *ngIf="template.ATT_STATUS === 1 || template.ATT_STATUS === 'QUEUED_FOR_DOWNLOAD'"
              color="warning" fill="solid" class="normal-case" shape="round" style="--color-contrast: white;">
              {{ 'Waiting for download' | translate }}
            </ion-button>
          
            <ion-button
              *ngIf="hasError(template.TMPLT_ID)"
              (click)="retryDownload($event, template)"
              color="danger"
              shape="round"
              class="btn-status">
              {{ 'Retry Download' | translate }}
            </ion-button>
          </ion-buttons>
        </ion-item>


      </ion-list>
    </ng-container>
  </ng-container>
</ng-container>

<!-- Message if no templates available -->
<ng-container *ngIf="!(isLoading) && (templates$ | async)?.length === 0">
  <p>No templates available.</p>
</ng-container>



  
  
</ion-content>
