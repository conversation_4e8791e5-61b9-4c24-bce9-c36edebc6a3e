<ion-header>
  <ion-toolbar color="primary">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="goBack()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ topicType }} Topic</ion-title>
    <ion-buttons slot="end">
      <ion-button (click)="saveTopic()" class="save-btn">
        SAVE
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>

<ion-content class="custom-content">

  <!-- Topic Details -->
  <div class="section-header">Topic Details</div>
  <div class="section-body">
    <div class="row">
      <div class="col-6">
        <label class="field-label">
          Select Topic <span class="required">*</span> :
        </label>
        <span class="select-link" (click)="openSelectList()">
          {{ newTopicDesc || 'Select' }}
          <ion-icon name="chevron-down-outline" *ngIf="!newTopicDesc"></ion-icon>
        </span>
      </div>
    </div>

    <!-- Date/Time -->
    <div class="date-time-section">
      <ion-item>
        <ion-label>Date & Time</ion-label>
        <ion-datetime
          [(ngModel)]="topicStart"
          [min]="minDate"
          [max]="maxDate"
          presentation="date-time"
          locale="en-GB-u-hc-h12"
          (ionChange)="onDatetimeChange($event)">
        </ion-datetime>
      </ion-item>

      <!-- Error message -->
      <ion-text color="danger" *ngIf="errorMessageForInvalidDateTime">
        {{ errorMessageForInvalidDateTime }}
      </ion-text>
    </div>
    <ion-item lines="none">
      <ion-label>{{ 'Max Allowed Topic Time' | translate }}:</ion-label>
      <ion-note slot="end">{{ maxAllowedTopicTime }}</ion-note>
      <ion-button size="small" *ngIf="isAllowedToUpdateTopicTime()" (click)="setMaxTopicTime()">
        {{ 'Set to Max Topic Time' | translate }}
      </ion-button>
    </ion-item>
    <!-- Description -->
    <div class="row">
      <div class="col-12">
        <label class="desc-label">Description / Additional Comments</label>
        <textarea
          class="desc-textarea"
          [(ngModel)]="TOPIC_NOTE"
          placeholder="Description / Additional Comments">
        </textarea>
      </div>
    </div>
  </div>

  <!-- Forms -->
  <div class="section-header">Forms</div>
  <div class="section-body">
    <div class="forms-row">
      <span class="no-forms">No forms added.</span>
      <button class="add-form-btn" (click)="addFormsToTopic()">
        <ion-icon name="add-circle-outline"></ion-icon>
      </button>
    </div>
  </div>

</ion-content>
