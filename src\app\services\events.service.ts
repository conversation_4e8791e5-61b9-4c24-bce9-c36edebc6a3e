import { Injectable } from '@angular/core';
import { Subject, Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Injectable({ providedIn: 'root' })
export class EventsService {
  private eventSubject = new Subject<{ name: string; data?: any }>();
  event$ = this.eventSubject.asObservable();
  private subscriptions = new Map<string, Subscription>();

  publish(name: string, data?: any) {
    this.eventSubject.next({ name, data });
  }

  subscribe(name: string, handler: (data: any) => void) {
    this.unsubscribe(name);
    const sub = this.event$
      .pipe(filter(evt => evt?.name === name))
      .subscribe(evt => handler(evt?.data));
    this.subscriptions.set(name, sub);
  }

  unsubscribe(name: string) {
    const sub = this.subscriptions.get(name);
    if (sub) {
      sub.unsubscribe();
      this.subscriptions.delete(name);
    }
  }
}