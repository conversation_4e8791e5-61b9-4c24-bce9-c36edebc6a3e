<ion-header color="primary" >
  <ion-toolbar color="primary">
     <ion-buttons slot="start">
      <ion-button fill="clear" (click)="close()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
     </ion-buttons>
    <ion-title>{{ listDetails.title }}</ion-title>
    <ion-buttons slot="start">
      <ion-button *ngIf="listDetails.multiSelect && !listDetails.hideCloseBtn" (click)="publishEvent({})">
        Close
      </ion-button>
    </ion-buttons>
    <ion-buttons slot="end">
      <ion-button *ngIf="listDetails.multiSelect" (click)="saveSelection()">Save</ion-button>
      <ion-button *ngIf="!listDetails.multiSelect" (click)="saveSelection(true)">Done</ion-button>
    </ion-buttons>
  </ion-toolbar>
  <ion-toolbar color="primary">
    <ion-searchbar #searchBar
                   placeholder="{{ listDetails.searchPlaceholder }}"
                   [(ngModel)]="listDetails.searchValue"
                   (ionInput)="filterList(listDetails.searchValue)">
    </ion-searchbar>
  </ion-toolbar>
</ion-header>

<ion-content [class.contrast]="styleTheme === 'contrast'">
  <ion-list *ngIf="listDetails.multiSelect">
    <ion-item *ngFor="let data of displayListData" (click)="toggleSelectData(data)" [class.has-error]="hasError">
      <ion-checkbox slot="start" [(ngModel)]="data[listDetails.selectKey]"></ion-checkbox>
      <ion-label>{{ data.DisplayString || '-' }}</ion-label>
    </ion-item>
  </ion-list>

  <ion-list *ngIf="!listDetails.multiSelect">
    <ion-item *ngFor="let data of displayListData" (click)="toggleRadioData(data)" [class.has-error]="hasError">
      <ion-checkbox slot="start" [checked]="data[listDetails.selectKey]"></ion-checkbox>
      <ion-label>{{ data.DisplayString || '-' }}</ion-label>
    </ion-item>
  </ion-list>
</ion-content>
