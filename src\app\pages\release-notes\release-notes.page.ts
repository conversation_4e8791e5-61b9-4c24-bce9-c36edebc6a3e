import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonButtons, IonContent, IonHeader, IonMenuButton, IonRouterOutlet, IonTitle, IonToolbar, MenuController } from '@ionic/angular/standalone';

@Component({
  selector: 'app-release-notes',
  templateUrl: './release-notes.page.html',
  styleUrls: ['./release-notes.page.scss'],
  standalone: true,
  imports: [ CommonModule, FormsModule , IonButtons, IonToolbar , IonMenuButton, IonHeader , IonContent, IonTitle]
})
export class ReleaseNotesPage implements OnInit {

  constructor( private routerOutlet: IonRouterOutlet,
      private menuCtrl: MenuController) { 
       this.routerOutlet.swipeGesture = false;
      this.menuCtrl.swipeGesture(true)
   }

  ngOnInit() {
  }

}
