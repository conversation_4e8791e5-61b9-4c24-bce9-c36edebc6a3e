import { Injectable } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PopoverController } from '@ionic/angular/standalone';
import { IonButton, IonInput, IonItem, ModalController, IonRouterOutlet, MenuController } from '@ionic/angular/standalone';
import { UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx'
import { Platform } from '@ionic/angular/standalone'
declare var cordova: any;
@Injectable({
  providedIn: 'root'
})
export class FormListDisplayService {


   constructor(
        private menuCtrl: MenuController, private unviredSDK: UnviredCordovaSDK,
        private platform: Platform, private alertController: AlertController) { 
  
        this.menuCtrl.swipeGesture(true)
    }
  

  /**
   * Setup URL monitoring for iframe to detect form button clicks
   */
  private setupIframeUrlMonitoring(iframe: any) {
    console.log('🔧 Setting up iframe URL monitoring...');
    
    let lastUrl = '';
    let lastHash = '';
    
    // Poll the iframe's location (this might be restricted by same-origin policy)
    const monitor = setInterval(() => {
      try {
        // Try to access iframe's location (may fail due to CORS)
        const currentUrl = iframe.contentWindow?.location?.href;
        const currentHash = iframe.contentWindow?.location?.hash;
        
        if (currentUrl && currentUrl !== lastUrl) {
          console.log('🔗 Iframe URL changed:', currentUrl);
          
          if (currentUrl.includes('ButtonClicked') || currentUrl.includes('buttonClicked')) {
            console.log('🔘 Button action detected in iframe URL:', currentUrl);
            this.handleFormButtonClick(currentUrl);
          }
          
          lastUrl = currentUrl;
        }
        
        if (currentHash && currentHash !== lastHash) {
          console.log('🔗 Iframe hash changed:', currentHash);
          
          if (currentHash.includes('ButtonClicked') || currentHash.includes('buttonClicked')) {
            console.log('🔘 Button action detected in iframe hash:', currentHash);
            this.handleFormButtonClick(currentHash.substring(1)); // Remove #
          }
          
          lastHash = currentHash;
        }
        
      } catch (e) {
        // Expected error due to same-origin policy
        // console.log('Cannot access iframe location due to CORS policy');
      }
    }, 500);
    
    // Store the monitor interval for cleanup
    (iframe as any).urlMonitor = monitor;
    
    console.log('✅ Iframe URL monitoring setup complete');
  }

  /**
   * Handle form button clicks detected from iframe
   */
  private async handleFormButtonClick(action: string) {
    console.log('🎯 Form button click action:', action);
    
    // Parse the action to identify the button type
    let buttonType = '';
    let randomNumber = '';
    
    if (action.includes('backButtonClicked')) {
      buttonType = 'back';
      randomNumber = action.replace('backButtonClicked', '');
    } else if (action.includes('saveAndExitButtonClicked')) {
      buttonType = 'saveAndExit';
      randomNumber = action.replace('saveAndExitButtonClicked', '');
    } else if (action.includes('saveButtonClicked')) {
      buttonType = 'save';
      randomNumber = action.replace('saveButtonClicked', '');
    } else if (action.includes('submitButtonClicked')) {
      buttonType = 'submit';
      randomNumber = action.replace('submitButtonClicked', '');
    } else if (action.includes('skipButtonClicked')) {
      buttonType = 'skip';
      randomNumber = action.replace('skipButtonClicked', '');
    } else if (action.includes('printButtonClicked')) {
      buttonType = 'print';
      randomNumber = action.replace('printButtonClicked', '');
    } else {
      console.log('🤷 Unknown button action:', action);
      return;
    }
    
    console.log(`✅ Detected ${buttonType} button click (${randomNumber})`);
    
    // Handle each button type
    switch (buttonType) {
      case 'back':
        await this.handleBackButtonClick();
        break;
      case 'save':
        await this.handleSaveButtonClick(false);
        break;
      case 'saveAndExit':
        await this.handleSaveButtonClick(true);
        break;
      case 'submit':
        await this.handleSubmitButtonClick();
        break;
      case 'skip':
        await this.handleSkipButtonClick();
        break;
      case 'print':
        await this.handlePrintButtonClick();
        break;
      default:
        console.log('🤷 Unhandled button type:', buttonType);
    }
  }

   async createEmbeddedIframe(htmlFileFolderPath: string) {
    console.log('htmlFileFolderPath' ,htmlFileFolderPath)
    try {
      console.log('Creating embedded iframe...');
      
      // Remove any existing iframe
      const existingIframe = document.getElementById('embedded-iframe');
      if (existingIframe) {
        existingIframe.remove();
      }
      
      // Create iframe container
      const iframeContainer = document.createElement('div');
      iframeContainer.id = 'iframe-container';
      iframeContainer.style.cssText = `
        position: fixed;
        top: 60px;
        left: 0;
        right: 0;
        bottom: 0;
        background: white;
        z-index: 1000;
        border-top: 2px solid #3880ff;
      `;
      
      // Create header with close button
      const header = document.createElement('div');
      header.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 50px;
        background: #3880ff;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        z-index: 1001;
      `;
      
      const title = document.createElement('span');
      title.innerHTML = 'Form Viewer';
      title.style.cssText = `
        color: white;
        font-size: 16px;
        font-weight: bold;
      `;
      
      const closeButton = document.createElement('button');
      closeButton.innerHTML = '✕ Close';
      closeButton.style.cssText = `
        background: rgba(255,255,255,0.2);
        color: white;
        border: none;
        padding: 8px 12px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      `;
      closeButton.onclick = () => {
        this.closeIframe();
      };
      
      header.appendChild(title);
      header.appendChild(closeButton);
      
      // Create iframe element
      const iframeElement = document.createElement('iframe');
      iframeElement.id = 'embedded-iframe';
      iframeElement.style.cssText = `
        width: 100%;
        height: calc(100% - 50px);
        margin-top: 50px;
        border: none;
      `;
      
      // Load the form HTML file
      const baseUrl = window.location.href.replace(/\/[^\/]*$/, '');
      const htmlPath = `${baseUrl}/assets/Crew Trailer Inspection Checklist/index.html`;
      // iframeElement.src = htmlPath;
      iframeElement.src = htmlFileFolderPath
      iframeElement.onload = (event: any) => {
        console.log('✅ Iframe loaded successfully');
        this.setupIframeUrlMonitoring(iframeElement);
      };
      
      iframeElement.onerror = () => {
        console.log('❌ Iframe failed to load, using fallback content...');
        const fallbackHTML = `
          <!DOCTYPE html>
          <html>
          <head><title>Form Viewer - Error</title></head>
          <body style="font-family: Arial; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
            <div style="text-align: center;">
              <h1>📱 Form Viewer</h1>
              <p>Could not load the form. Please try again.</p>
              <button onclick="window.location.reload()">Reload</button>
            </div>
          </body>
          </html>
        `;
        iframeElement.src = 'data:text/html,' + encodeURIComponent(fallbackHTML);
      };
      
      // Add elements to container
      iframeContainer.appendChild(header);
      iframeContainer.appendChild(iframeElement);
      
      // Add container to page
      document.body.appendChild(iframeContainer);
      
      console.log('Embedded iframe created successfully');
      console.log('Iframe source:', iframeElement.src);
      
      await this.showAlert('Success', 'Form opened successfully.');
      
    } catch (error) {
      console.error('Error creating embedded iframe:', error);
      throw error;
    }
  }

  /**
   * Handle back button click from form
   */
  private async handleBackButtonClick() {
    console.log('⬅️ Back button clicked in form');
    
    const shouldGoBack = await this.showConfirmDialog(
      'Go Back', 
      'Are you sure you want to go back? Any unsaved changes may be lost.'
    );
    
    if (shouldGoBack) {
      console.log('✅ User confirmed back navigation');
      this.closeIframe();
    }
  }

  /**
   * Handle save button click from form
   */
  private async handleSaveButtonClick(exit: boolean) {
    console.log(`💾 Save button clicked (exit: ${exit})`);
    
    await this.showAlert('Save Action', 
      exit ? 'Form saved and will exit' : 'Form saved successfully'
    );
    
    if (exit) {
      setTimeout(() => {
        this.closeIframe();
      }, 2000);
    }
  }

  /**
   * Handle submit button click from form
   */
  private async handleSubmitButtonClick() {
    console.log('📤 Submit button clicked in form');
    
    await this.showAlert('Submit Action', 'Form submitted successfully');
    
    setTimeout(() => {
      this.closeIframe();
    }, 2000);
  }

  /**
   * Handle skip button click from form
   */
  private async handleSkipButtonClick() {
    console.log('⏭️ Skip button clicked in form');
    
    const shouldSkip = await this.showConfirmDialog(
      'Skip Form', 
      'Are you sure you want to skip this form?'
    );
    
    if (shouldSkip) {
      await this.showAlert('Skip Action', 'Form skipped');
      this.closeIframe();
    }
  }

  /**
   * Handle print button click from form
   */
  private async handlePrintButtonClick() {
    console.log('🖨️ Print button clicked in form');
    console.log('Print action triggered - handled by form');
  }

  /**
   * Close the iframe
   */
  private closeIframe() {
    console.log('🔒 Closing iframe...');
    
    const iframeContainer = document.getElementById('iframe-container');
    if (iframeContainer) {
      // Clear any URL monitoring intervals
      const iframe = document.getElementById('embedded-iframe') as any;
      if (iframe && iframe.urlMonitor) {
        clearInterval(iframe.urlMonitor);
      }
      
      iframeContainer.remove();
      console.log('✅ Iframe closed');
    }
  }

  /**
   * Generate JSON from the iframe form
   */
  async generateJson() {
    console.log('🔍 Generating JSON from iframe...');
    
    try {
      // Check if iframe is open
      const iframe = document.getElementById('embedded-iframe') as any;
      
      if (!iframe) {
        await this.showAlert('No Form', 'Please open a form first to generate JSON.');
        return;
      }
      
      console.log('📤 Calling generateJSON function in iframe...');
      
      // Try to call generateJSON function directly in iframe
      try {
        if (iframe.contentWindow && iframe.contentWindow.generateJSON) {
          const jsonResult = iframe.contentWindow.generateJSON();
          console.log('📊 JSON generated:', jsonResult);
          
          // Format the JSON for better display
          let formattedJson = jsonResult;
          try {
            const parsed = JSON.parse(jsonResult);
            formattedJson = JSON.stringify(parsed, null, 2);
          } catch (e) {
            console.log('Result is not valid JSON, using as is');
          }
          
          await this.showAlert('JSON Generated', `Form data:\n\n${formattedJson}`);
          
        } else {
          throw new Error('generateJSON function not available in iframe');
        }
      } catch (error) {
        console.error('❌ Error calling generateJSON:', error);
        await this.showAlert('Error', 'Could not generate JSON. The form may not be fully loaded or the generateJSON function is not available.');
      }
      
    } catch (error) {
      console.error('❌ Error generating JSON:', error);
      await this.showAlert('Error', `Failed to generate JSON: ${error}`);
    }
  }

  /**
   * Show confirmation dialog
   */
  private async showConfirmDialog(header: string, message: string): Promise<boolean> {
    return new Promise(async (resolve) => {
      const alert = await this.alertController.create({
        header,
        message,
        buttons: [
          {
            text: 'Cancel',
            role: 'cancel',
            handler: () => resolve(false)
          },
          {
            text: 'OK',
            handler: () => resolve(true)
          }
        ]
      });
      await alert.present();
    });
  }
  
  private async showAlert(header: string, message: string) {
    const alert = await this.alertController.create({
      header,
      message,
      buttons: ['OK']
    });
    // await alert.present();
  }

  
}
