import { Component, <PERSON>Init, Ng<PERSON><PERSON> } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { DataService } from 'src/app/services/data.service';
import { BusyIndicatorService } from 'src/app/services/busy-indicator.service';
import { UnviredCordovaSDK, ResultType } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { UtilityService } from 'src/app/services/utility.service';
import { PrintSTMRFormService } from 'src/app/services/printStmr.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  IonGrid,
  IonRow,
  IonCol,
  IonLabel,
  IonInput,
  IonButton,
  IonIcon,
  IonCard,
  IonCardHeader,
  IonCardContent,
  IonCardTitle,
  IonHeader,
  IonToolbar,
  IonTitle,
  IonButtons,
  IonContent,
  IonItem,
  IonRadio,
  IonRadioGroup,
  IonThumbnail,
  ModalController,
  AlertController,
  IonSearchbar,
  ToastController,
  PopoverController
} from '@ionic/angular/standalone';
import { addIcons } from 'ionicons';
import { AppConstants } from 'src/app/constants/appConstants';
import { SelectListPage } from '../select-list/select-list.page';
import { TopicsPage } from '../topics/topics.page';
import { 
  personCircleOutline, 
  sendOutline, 
  chevronDownOutline, 
  arrowBackOutline, 
  cogOutline,
  trashOutline,
  personAddOutline
} from 'ionicons/icons';
import { STMR_ACTION } from 'src/models/STMR_ACTION';
import { SignaturePage } from '../signature/signature.page';
import { STMR_TOPIC } from 'src/models/STMR_TOPIC';
import { STMR_HSE_TOPIC } from 'src/models/STMR_HSE_TOPIC';
import { STMR_CTA_TOPIC } from 'src/models/STMR_CTA_TOPIC';
import { CTA_HEADER } from 'src/models/CTA_HEADER';
import { STMR_FORM } from 'src/models/STMR_FORM';
import { HSE_STANDARD_HEADER } from 'src/models/HSE_STANDARD_HEADER';
import { StmrOptionsPopoverPage } from '../stmr-options-popover/stmr-options-popover.page';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { filter, take } from 'rxjs/operators';
import { selectPrefilledData } from 'src/app/store/store.selector'

addIcons({
  'person-circle': personCircleOutline,
  'send-outline': sendOutline,
  'chevron-down-outline': chevronDownOutline,
  'arrow-back-outline': arrowBackOutline,
  'cog-outline': cogOutline,
  'trash-outline': trashOutline,
  'person-add-outline': personAddOutline
});

@Component({
  selector: 'app-stmr-details',
  templateUrl: './stmr-details.page.html',
  styleUrls: ['./stmr-details.page.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IonGrid,
    IonRow,
    IonCol,
    IonLabel,
    IonInput,
    IonButton,
    IonIcon,
    IonCard,
    IonCardHeader,
    IonCardContent,
    IonCardTitle,
    IonHeader,
    IonToolbar,
    IonTitle,
    IonButtons,
    IonContent,
    IonItem,
    IonRadio,
    IonRadioGroup,
    IonThumbnail,
    SelectListPage,
    IonSearchbar,
  ]
})
export class STMRDetailsPage implements OnInit {
  stmrHeader: any = {};
  stmrTopicEntity: any[] = [];
  stmrCrew: any[] = [];
  isSTMRComplete: boolean = false;
  styleTheme: string = '';
  isLoading: boolean = false;
  loadingMessage: string = '';
  isSTMRUpdated = false;
  timestampOfLastTap: number = 0;
  isPageActive = true;
  prefillData: any;
  autoFillData: any = {};
  stmrActions: STMR_ACTION[] = [];

  constructor(
    private modalController: ModalController,
    private alertController: AlertController,
    private translate: TranslateService,
    private ngZone: NgZone,
    private dataService: DataService,
    private unviredSDK: UnviredCordovaSDK,
    private busyIndicatorService: BusyIndicatorService,
    private toastController: ToastController,
    private UtilityService: UtilityService,
    private popoverCtrl: PopoverController,
    private printSTMRForm: PrintSTMRFormService,
    private router: Router,
    private store: Store<any>
  ) { }

  ngOnInit() {
    // this.store.select(selectPrefilledData)
    //   .pipe(filter(data => !!data)) // only if data exists
    //   .subscribe(data => {
    //     this.handlePrefill(data);
    //   });
      this.store.select(selectPrefilledData).pipe(
      filter(data => !!data),
      take(1)
    ).subscribe(data => this.handlePrefill(data));
  }

  private handlePrefill(data: any) {
    console.log('Prefill data in component (from store):', data);
    this.autoFillData = data;
    this.initializeSTMRFromPrefill(data);
  }

  
   initializeSTMRFromPrefill(data: any) {
    this.stmrHeader = {
      STMR_ID : data.STMR_ID || 'New',
      COMPANY: data.COMPANY || '',
      RIG_NO: data.RIG_NO || '',
      RIG_TYPE: data.RIG_TYPE || '',
      SHIFT: data.SHIFT || '',
      SHIFT_TIME: data.SHIFT_TIME || new Date().toISOString(),
      WELL_LOC: data.WELL_LOC || '',
      CHAIRED_BY: data.CHAIRED_BY || '',
      OPERATOR: {
        NAME: data.OPERATOR?.NAME || '',
        SIGN: data.OPERATOR?.SIGN || ''
      },
      ONSITE_SUP: data.ONSITE_SUP || '',
      ONSITE_SUP_SIGN: '',
      STMR_STATUS: '',
      SYNC_STATUS: '',
    };
  // after fetching prefills or data:
if (Array.isArray(data.OPERATOR) && data.OPERATOR.length > 0) {
  this.stmrHeader.OPERATOR = data.OPERATOR[0];
} else if (data.OPERATOR && typeof data.OPERATOR === 'object') {
  this.stmrHeader.OPERATOR = data.OPERATOR;
} else {
  this.stmrHeader.OPERATOR = { NAME: '' };
}
    
    this.stmrCrew = data.CREW ? [...data.CREW] : [];
    this.stmrTopicEntity = data.TOPICS ? [...data.TOPICS] : [];
    this.markSTMRAsPristine();
  }

  async addTopic() {
    const modal = await this.modalController.create({
      component: TopicsPage,
      cssClass: 'full-screen-modal',
      componentProps: { topic: null }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.topic) {
      this.stmrTopicEntity.push({ topic: data.topic, forms: [], data: [] });
      this.markSTMRAsUpdated();
    }
  }

  async editTopic(entity: any, i: number) {
    const modal = await this.modalController.create({
      component: TopicsPage,
      cssClass: 'full-screen-modal',
      componentProps: { topic: entity.topic }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.topic) {
      this.stmrTopicEntity[i].topic = data.topic;
      this.markSTMRAsUpdated();
    }
  }

  async removeTopic(topic: any, idx: number) {
    const alert = await this.alertController.create({
      header: 'Delete Topic',
      message: `Are you sure you want to delete this topic?`,
      buttons: [
        { text: 'Cancel', role: 'cancel' },
        { text: 'Delete', role: 'destructive', handler: () => {
            this.stmrTopicEntity[idx].topic.P_MODE = 'D';
            this.markSTMRAsUpdated();
          } }
      ]
    });
    await alert.present();
  }

  async addCrew() {
    const modal = await this.modalController.create({
      component: SelectListPage,
      cssClass: 'full-screen-modal',
      componentProps: { crew: null }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.crew) {
      this.stmrCrew.push({ ...data.crew, P_MODE: 'A' });
      this.markSTMRAsUpdated();
    }
  }

  async editCrew(crew: any, i: number) {
    const modal = await this.modalController.create({
      component: SelectListPage,
      cssClass: 'full-screen-modal',
      componentProps: { crew }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data && data.crew) {
      this.stmrCrew[i] = { ...data.crew, P_MODE: 'M' };
      this.markSTMRAsUpdated();
    }
  }

  async removeCrewMember(crew: any) {
    const alert = await this.alertController.create({
      header: 'Delete Crew',
      message: `Are you sure you want to delete ${crew.CREW_NAME || 'this member'}?`,
      buttons: [
        { text: 'Cancel', role: 'cancel' },
        { text: 'Delete', role: 'destructive', handler: () => {
            const idx = this.stmrCrew.indexOf(crew);
            if (idx > -1) {
              if (this.stmrCrew[idx].CREW_TYPE !== 'THIRD_PARTY') {
                this.stmrCrew[idx].P_MODE = 'D';
              } else {
                this.stmrCrew.splice(idx, 1);
              }
              this.markSTMRAsUpdated();
            }
          } }
      ]
    });
    await alert.present();
  }
  async captureSign(type: 'ONSITE_SUP_SIGN' | 'CREW_SIGN' | 'CREW_SUPERVISOR_SIGN', crew?: any, idx?: number) {
    if (type === 'CREW_SIGN' && (!crew?.CREW_NAME || crew.CREW_NAME.trim().length < 1)) {
      await this.showAlert('Alert', 'Please enter the Crew Name first.');
      return;
    }
    const modal = await this.modalController.create({
      component: SignaturePage,
      componentProps: { signatureType: type, crew }
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data?.signatureType === type) {
      if (type === 'CREW_SIGN' && typeof idx === 'number') {
        this.stmrCrew[idx].CREW_SIGN = data.CREW_SIGN || '';
      }
      if (type === 'ONSITE_SUP_SIGN') {
        this.stmrHeader.ONSITE_SUP_SIGN = data.CREW_SIGN || '';
      }
      if (type === 'CREW_SUPERVISOR_SIGN') {
        this.stmrHeader.CREW_SUPERVISOR_SIGN = data.CREW_SIGN || '';
      }
      this.markSTMRAsUpdated();
    }
  }

  async openSelectCrew(field: string, value: any, displayValue: any) {
    // You can create a SelectCrewModalComponent similarly if needed
    const alert = await this.alertController.create({
      header: 'Select Crew',
      message: 'This would open a crew selection modal.',
      buttons: ['OK']
    });
    await alert.present();
  }

  close() {
    this.modalController.dismiss();
    this.router.navigate(['/forms']);
  }

  async presentSettingsPopover(event: UIEvent): Promise<void> {
  const popover = await this.popoverCtrl.create({
    component: StmrOptionsPopoverPage,
    event,
    translucent: true
  });

  await popover.present();

  const { data } = await popover.onDidDismiss();

  if (!data) {
    return;
  }

  if (data.changeTheme) {
    this.styleTheme = this.styleTheme === 'normal' ? 'contrast' : 'normal';
  } else if (data.print) {
    try {
      // Extract topics from stmrTopicEntity
      const topics: STMR_TOPIC[] = this.stmrTopicEntity.map(entity => entity.topic);

      const STMRTopicsWithHSENames = await this.updateHSENamesForSTMRTopics(topics);
      const STMRTopicsWithCTANames = await this.updateCTANamesForSTMRTopics(STMRTopicsWithHSENames);
      const STMRTopicWithCTAForms = await this.updateFormsForCTATopic(STMRTopicsWithCTANames);

      // Print STMR
      this.printSTMRForm.printSTMRForm(this.stmrHeader.STMR_ID, {
        stmrHeader: this.stmrHeader,
        stmrTopic: STMRTopicWithCTAForms,
        stmrCrew: this.stmrCrew
      });

    } catch (error) {
      console.error('Error while preparing STMR for print:', error);
    }
  }
}

async updateHSENamesForSTMRTopics(stmrTopics: STMR_TOPIC[]): Promise<STMR_HSE_TOPIC[]> {
  if (!stmrTopics.length) return [];

  const HSEIds = stmrTopics.map(item => item.STD_ID).filter(Boolean);

  if (!HSEIds.length) {
    return stmrTopics.map(item => new STMR_HSE_TOPIC(item));
  }

  const inClause = HSEIds.map(id => `'${id}'`).join(',');
  const query = `SELECT * FROM HSE_STANDARD_HEADER WHERE STD_ID IN (${inClause})`;

  const result = await this.unviredSDK.dbExecuteStatement(query);

  if (result.type !== ResultType.success) {
    console.error('Failed to fetch HSE headers');
    return stmrTopics.map(item => new STMR_HSE_TOPIC(item));
  }
  const hseMap: Map<string, string> = new Map<string, string>(
  result.data.map((hse: HSE_STANDARD_HEADER) => [hse.STD_ID, hse.NAME])
 );
  return stmrTopics.map(item => {
    const hseTopic = new STMR_HSE_TOPIC(item);
    hseTopic.STD_TYPE = hseMap.get(item.STD_ID) || '';
    return hseTopic;
  });
}

async updateCTANamesForSTMRTopics(stmrTopics: STMR_TOPIC[]): Promise<STMR_CTA_TOPIC[]> {
  if (!stmrTopics.length) return [];

  const CTAIds = stmrTopics.map(item => item.CTA_ID).filter(Boolean);

  if (!CTAIds.length) {
    return stmrTopics.map(item => new STMR_CTA_TOPIC(item));
  }

  const inClause = CTAIds.map(id => `'${id}'`).join(',');
  const query = `SELECT * FROM CTA_HEADER WHERE CTA_ID IN (${inClause})`;

  const result = await this.unviredSDK.dbExecuteStatement(query);

  if (result.type !== ResultType.success) {
    console.error('Failed to fetch CTA headers');
    return stmrTopics.map(item => new STMR_CTA_TOPIC(item));
  }

  const ctaMap: Map<string, string> = new Map<string, string>(
  result.data.map((cta: CTA_HEADER) => [cta.CTA_ID, cta.NAME])
);


  return stmrTopics.map(item => {
    const ctaTopic = new STMR_CTA_TOPIC(item);
    ctaTopic.CTA_TYPE = ctaMap.get(item.CTA_ID) || '';
    return ctaTopic;
  });
}

async updateFormsForCTATopic(stmrWithCTATopics: STMR_CTA_TOPIC[]): Promise<STMR_CTA_TOPIC[]> {
  if (!stmrWithCTATopics.length) return [];

  const stmrId = stmrWithCTATopics[0].STMR_ID;

  const result = await this.unviredSDK.dbSelect("STMR_FORM", `STMR_ID = '${stmrId}'`);

  if (result.type !== ResultType.success || !result.data?.length) {
    console.error("Error fetching forms for CTA topics or no data");
    return stmrWithCTATopics;
  }

  const forms: STMR_FORM[] = result.data;

  stmrWithCTATopics.forEach(topic => {
    const formsForTopic = forms.filter(form => form.TOPIC_NO === topic.TOPIC_NO);
    topic.FORMS = formsForTopic.map(f => f.NAME);
  });

  return stmrWithCTATopics;
}
  
  async localSaveSTMR(): Promise<void> {
    const now = Date.now();
    if (now - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      this.unviredSDK.logInfo('STMRDetailsPage', 'localSaveSTMR', 'Ignoring double tap');
      return;
    }
    this.timestampOfLastTap = now;

    if (this.stmrHeader.SYNC_STATUS === AppConstants.SYNC_STATUS.QUEUED) {
      // Save to server queue directly if already queued
      await this.saveSTMRToServer();
      return;
    }

    try {
      await this.busyIndicatorService.displayBusyIndicator(this.translate.instant("Saving STMR. Please wait..."));
      await this.prepareAndSaveSTMR();
      const toast = await this.toastController.create({
        message: this.translate.instant('STMR Changes saved successfully'),
        duration: 1000,
        position: 'bottom'
      });
      await toast.present();
      this.markSTMRAsPristine();
    } catch (error:any) {
      await this.busyIndicatorService.hideBusyIndicator();
      this.unviredSDK.logError('STMRDetailsPage', 'localSaveSTMR', 'Error while saving STMR to DB');
      await this.showAlert(this.translate.instant("Error"), this.translate.instant("Error while saving STMR: ") + error);
      throw error;
    }
  }

  /**
   * Save STMR into the server queue and submit the data
   */
  async saveSTMRToServer(): Promise<void> {
    try {
      await this.busyIndicatorService.displayBusyIndicator(this.translate.instant("Queueing STMR to server. Please wait..."));
      await this.prepareAndSaveSTMR();

      await this.submitToServer();

      await this.busyIndicatorService.hideBusyIndicator();
      this.markSTMRAsPristine();
    } catch (error) {
      await this.busyIndicatorService.hideBusyIndicator();
      this.unviredSDK.logError('STMRDetailsPage', 'saveSTMRToServer', 'Error while queueing STMR to server');
      await this.showAlert(
        this.translate.instant("Error"),
        this.translate.instant("Error while submitting STMR to server: ") + JSON.stringify(error, null, 2)
      );
      throw error;
    }
  }

  async saveAndExitSTMR(): Promise<void> {
    const now = Date.now();
    if (now - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      this.unviredSDK.logInfo("STMRDetailsPage", "saveAndExitSTMR", "Ignoring double tap");
      return;
    }
    this.timestampOfLastTap = now;
    this.isPageActive = false;

    try {
      await this.busyIndicatorService.displayBusyIndicator(this.translate.instant("Queueing STMR to server. Please wait..."));
      await this.prepareAndSaveSTMR();
      await this.submitToServer();
      await this.busyIndicatorService.hideBusyIndicator();
      this.goToFormsPage();
    } catch (error) {
      this.isPageActive = true;
      await this.busyIndicatorService.hideBusyIndicator();
      this.unviredSDK.logError("STMRDetailsPage", "saveAndExitSTMR", "Error while queueing STMR to server");
      await this.showAlert(
        this.translate.instant("Error"),
        this.translate.instant("Error while submitting STMR to server: ") + JSON.stringify(error, null, 2)
      );
    }
  }

  /**
   * Prepare and save STMR only if it is not already sent (check using Unvired SDK)
   */
  prepareAndSaveSTMR(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.unviredSDK.logInfo("STMRDetailsPage", "prepareAndSaveSTMR", "Checking if STMR is in sent items.");
      this.unviredSDK.isInSentItem(this.stmrHeader.LID).then(resultInSent => {
        const isSent = (String(resultInSent.data).toLowerCase() === "true" || resultInSent.data === AppConstants.BOOL_TRUE);
        if (isSent) {
          this.unviredSDK.logInfo("STMRDetailsPage", "prepareAndSaveSTMR", "STMR is already sent to server.");
          this.unviredSDK.getMessages(); 
          this.showAlert(
            this.translate.instant("Previous request still being reconciled!"),
            this.translate.instant("STMR could not be submitted. Please wait till response is received.")
          );
          reject('Already sent');
        } else {
          this.unviredSDK.logInfo("STMRDetailsPage", "prepareAndSaveSTMR", "Saving STMR with latest changes.");
          // this.saveSTMRIntoDB().then(() => resolve()).catch((err:any) => reject(err));
        }
      }).catch(err => {
        this.unviredSDK.logError("STMRDetailsPage", "prepareAndSaveSTMR", err);
        reject(err);
      });
    });
  }
  saveSTMRIntoDB() {
    throw new Error('Method not implemented.');
  }

  /**
   * Validation and submitting STMR
   */
  async submitSTMR(): Promise<void> {
    const now = Date.now();
    if (now - this.timestampOfLastTap <= AppConstants.DOUBLE_TAP_IGNORE_DURATION) {
      this.unviredSDK.logInfo("STMRDetailsPage", "submitSTMR", "Ignoring double tap");
      return;
    }
    this.timestampOfLastTap = now;
    this.isPageActive = false;

    try {
      if (!this.validateSTMR(false, true)) {
        this.isPageActive = true;
        return;
      }

      if (!this.areAllFormsCompleted()) {
        this.isPageActive = true;
        await this.showAlert("Alert", "It seems like some forms are not filled completely.");
        return;
      }

      let stmrAction = this.createSTMRAction(this.stmrHeader);
      this.stmrActions = [...this.stmrActions, stmrAction];
      this.stmrHeader.STMR_STATUS = AppConstants.VAL_FORM_STATUS.SUBM;

      await this.busyIndicatorService.displayBusyIndicator(this.translate.instant("Submitting STMR. Please wait..."));
      
      await this.prepareAndSaveSTMR();
      await this.submitToServer();

      await this.busyIndicatorService.hideBusyIndicator();
      this.goToFormsPage();

    } catch (error) {
      this.isPageActive = true;
      await this.busyIndicatorService.hideBusyIndicator();
      this.unviredSDK.logError("STMRDetailsPage", "submitSTMR", "Error while submitting STMR to server");
      await this.showAlert(
        this.translate.instant("Error"),
        this.translate.instant("Error while submitting STMR to server: ") + JSON.stringify(error, null, 2)
      );
    }
  }
  submitToServer() {
    throw new Error('Method not implemented.');
  }



  goToFormsPage() {
    // Navigate to forms page using Angular router or Ionic Router
  }

  areAllFormsCompleted(): boolean {
    // Your logic to check form completeness
    return true;
  }

  private createSTMRAction(stmrHeader: { STMR_ID: string; LID: string }): STMR_ACTION {
  this.markSTMRAsUpdated();

  const stmrActionObj = new STMR_ACTION();

  stmrActionObj.STMR_ID = stmrHeader.STMR_ID;
  stmrActionObj.FORM_ID = stmrHeader.STMR_ID;
  stmrActionObj.ACTION_CODE = AppConstants.ACTION_CODE.COMPLETE;
  stmrActionObj.P_MODE = 'A';
  stmrActionObj.FID = stmrHeader.LID;
  stmrActionObj.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;

  return stmrActionObj;
}

 validateSTMR(noAlert = false, isSubmit = false): boolean {
    let keyMissing = false, deleteCount = 0;

    if (!this.stmrHeader.SHIFT) {
      if (!noAlert) this.showAlert('Alert', 'Please select one of the shift types.');
    } else if (!this.stmrHeader.WELL_LOC?.trim()) {
      if (!noAlert) this.showAlert('Alert', 'Please provide a valid Location.');
    } else if (!this.stmrHeader.CHAIRED_BY?.trim()) {
      if (!noAlert) this.showAlert('Alert', 'Please provide a valid "Chaired By" name.');
    } else if (!this.stmrHeader.OPERATOR?.trim()) {
      if (!noAlert) this.showAlert('Alert', 'Please provide a valid Operator name.');
    } else if (!this.stmrCrew || this.stmrCrew.length === 0) {
      if (!noAlert) this.showAlert('Alert', 'Please add a minimum of one crew member.');
    } else {
      const activeCrew = this.stmrCrew.filter(c => c.P_MODE !== 'D');
      const nonThirdPartyCrew = activeCrew.filter(c => c.CREW_TYPE !== 'THIRD_PARTY');
      if (nonThirdPartyCrew.length === 0) {
        if (!noAlert) this.showAlert('Alert', 'Please add at least one non-third-party crew member.');
        keyMissing = true;
      } else {
        for (let crew of nonThirdPartyCrew) {
          if (!crew.CREW_SIGN || !crew.CREW_NAME?.trim()) {
            if (!noAlert) this.showAlert('Alert', 'Please capture names & signatures for all crew members.');
            keyMissing = true;
            break;
          }
        }
      }
      deleteCount = this.getNoOfDeletedCrew(this.stmrCrew);
      if (this.stmrCrew.length === deleteCount) {
        if (!noAlert) this.showAlert('Alert', 'Please add a minimum of one crew member.');
      } else if (!keyMissing) {
        if (!this.stmrTopicEntity || this.stmrTopicEntity.length === 0) {
          if (!noAlert) this.showAlert('Alert', 'Please add a minimum of one topic.');
        } else {
          deleteCount = this.getNoOfDeletedTopics(this.stmrTopicEntity);
          if (this.stmrTopicEntity.length === deleteCount) {
            if (!noAlert) this.showAlert('Alert', 'Please add a minimum of one topic.');
          } else if (isSubmit) {
            if (!this.stmrHeader.ONSITE_SUP?.trim()) {
              if (!noAlert) this.showAlert('Alert', 'Please provide a valid Crew Supervisor name.');
            } else if (!this.stmrHeader.ONSITE_SUP_SIGN) {
              if (!noAlert) this.showAlert('Alert', 'Please provide Crew Supervisor Signature.');
            } else {
              return true;
            }
          } else {
            return true;
          }
        }
      }
    }
    return false;
  }


  getNoOfDeletedCrew(crewList: any[]): number {
    return crewList.filter(c => c.P_MODE === 'D').length;
  }

  getNoOfDeletedTopics(topicList: any[]): number {
    return topicList.filter(t => t.topic?.P_MODE === 'D').length;
  }

  isSTMRReadonly(stmrHeader: any): boolean {
    // Dummy logic, update as needed
    return false;
  }

  getSTMRIDForDisplay(formId: string): string {
    return formId?.startsWith('New') ? 'New' : formId;
  }

  async onSelectingShiftType(event: any) {
  // Optional: Show a quick empty toast (placeholder for UI refresh)
  const toast = await this.toastController.create({
    message: '',           // Add a message if you want, else leave blank
    duration: 10,          // 10ms, basically invisible and just triggers change detection
    position: 'bottom'
  });
  await toast.present();

  setTimeout(() => {
    this.unviredSDK.logInfo(
      "STMRDetailsPage",
      "onSelectingShiftType",
      "shift type " + this.stmrHeader.SHIFT
    );
    this.onModelChange(event); // trigger your handler with the new shift value/event
  }, 100);
}

public returnDisplayDate(time: any, fromFormat?: string, toFormat?: string): string {
  return this.UtilityService.returnDisplayDate(time, fromFormat, toFormat);
}

  onModelChange(value: any): void {
    // Dummy implementation
    console.log('Model changed:', value);
  }

  dispTime(date: string): string {
    if (!date) return '';
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';
    return d.toLocaleString();
  }

  getNoOfNonThirdPartyCrewMembers(stmrCrew: any[]): number {
    return (stmrCrew || []).filter(c => c.CREW_TYPE !== 'THIRD_PARTY' && c.P_MODE !== 'D').length;
  }
  addThirdPartyCrew(): void {
    // Dummy implementation
    console.log('Add Third Party Crew');
  }

  getNoOfThirdPartyCrewMembers(stmrCrew: any[]): number {
    return (stmrCrew || []).filter(c => c.CREW_TYPE === 'THIRD_PARTY' && c.P_MODE !== 'D').length;
  }

  addCrewSupervisor(): void {
    // Dummy implementation
    console.log('Add Crew Supervisor');
  }

  removeCrewSupervisor(): void {
    // Dummy implementation
    console.log('Remove Crew Supervisor');
  }

    markSTMRAsPristine() {
    this.isSTMRUpdated = false;
    this.ngZone.run(() => {
      this.isSTMRComplete = this.validateSTMR(true, true);
    });
  }
  markSTMRAsUpdated() {
    this.isSTMRUpdated = true;
    this.ngZone.run(() => {
      this.isSTMRComplete = this.validateSTMR(true, true);
    });
  }
    async showAlert(header: string, message: string) {
    const a = await this.alertController.create({ header, message, buttons: ['OK'] });
    await a.present();
  }
}