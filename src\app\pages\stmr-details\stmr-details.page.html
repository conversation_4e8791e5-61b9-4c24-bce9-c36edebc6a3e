<ion-header>
  <ion-toolbar color="primary" class="custom-toolbar">
    <ion-buttons slot="start">
      <ion-button fill="clear" (click)="close()">
        <ion-icon name="arrow-back-outline"></ion-icon>
      </ion-button>
    </ion-buttons>
    <ion-title>{{ 'STMR Details' | translate }}</ion-title>

    <ion-buttons slot="end">
      <ion-button fill="clear" (click)="presentSettingsPopover($event)">
        <i class="fa-solid fa-gear"></i>
      </ion-button>

      <ion-button fill="solid"
                  class="btn-stmr-save"
                  (click)="localSaveSTMR()"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        {{ 'Save' | translate }}
      </ion-button>

      <ion-button fill="solid"
                  class="btn-stmr-save"
                  (click)="saveAndExitSTMR()"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        {{ 'Save and Exit' | translate }}
      </ion-button>

      <ion-button fill="solid"
                  color="primary"
                  class="btn-send"
                  (click)="submitSTMR()"
                  [disabled]="!isSTMRComplete"
                  *ngIf="!isSTMRReadonly(stmrHeader)">
        <i class="fa-solid fa-paper-plane"></i>
        {{ 'Send' | translate }}
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>


<ion-content [class.contrast]="styleTheme === 'contrast'">

  <!-- Details Section -->
  <ion-header class="section-header">
  <ion-toolbar class="section-toolbar">
    <ion-title slot="start" class="section-title">{{ 'Details' | translate }}</ion-title>
  </ion-toolbar>
</ion-header>

  <ion-card>
    <ion-card-content>
      <ion-grid>
        <ion-row>
          <ion-col size="12" size-md="6" size-xl="4">
            <ion-label>{{ 'Company #' | translate }}:</ion-label>
            <div class="value-display">{{ stmrHeader?.COMPANY || '-' }}</div>
          </ion-col>
          <ion-col size="12" size-md="6" size-xl="4">
            <ion-label>{{ 'Site #' | translate }}:</ion-label>
            <div class="value-display">{{ stmrHeader?.RIG_NO || '-' }}</div>
          </ion-col>
          <ion-col size="12" size-md="6" size-xl="4">
            <ion-label>{{ 'STMR #' | translate }}:</ion-label>
            <div class="value-display">{{ getSTMRIDForDisplay(stmrHeader?.STMR_ID) }}</div>
          </ion-col>
          <ion-col size="12" size-md="6" size-xl="4">
            <ion-label>{{ 'Created' | translate }}:</ion-label>
            <div class="value-display">{{ returnDisplayDate(stmrHeader?.SHIFT_TIME, '', 'MMM DD YYYY HH:mm') || '-' }}</div>
          </ion-col>
          <ion-col size="12" size-md="6" size-xl="4" class="stmr-header-items">
          <div class="my-3py-2" [ngClass]="{'has-error': !stmrHeader.SHIFT}">
            {{'Shift Type' | translate}}:&nbsp;
            <ion-radio-group
              [(ngModel)]="stmrHeader.SHIFT"
              [disabled]="isSTMRReadonly(stmrHeader)"
              (ionChange)="onSelectingShiftType($event.detail.value)">
              <ion-item>
                <ion-label>{{ 'Night Shift' | translate }}</ion-label>
                <ion-radio slot="start" value="NIGHT"></ion-radio>
              </ion-item>
              <ion-item>
                <ion-label>{{ 'Day Shift' | translate }}</ion-label>
                <ion-radio slot="start" value="DAY"></ion-radio>
              </ion-item>
            </ion-radio-group>
          </div>
        </ion-col>
          <ion-col size="12" size-md="6" size-xl="4">
            <ion-label>{{ 'Location' | translate }}:</ion-label>
            <ion-input [(ngModel)]="stmrHeader.WELL_LOC" [disabled]="isSTMRReadonly(stmrHeader)" (ionChange)="onModelChange($event.detail.value)"></ion-input>
          </ion-col>
          <ion-col size="12" size-md="6" size-xl="4">
            <ion-label class="red-label">{{ 'Chaired By' | translate }}:</ion-label>
            <ion-input class="red-underline"
                       [(ngModel)]="stmrHeader.CHAIRED_BY"
                       [disabled]="isSTMRReadonly(stmrHeader)"
                       (focus)="openSelectCrew('CHAIRED_BY', stmrHeader.CHAIRED_BY, stmrHeader.CHAIRED_BY)"
                       (ionChange)="onModelChange($event.detail.value)">
            </ion-input>
            <ion-button slot="end" fill="clear" *ngIf="!isSTMRReadonly(stmrHeader)" (click)="openSelectCrew('CHAIRED_BY', stmrHeader.CHAIRED_BY, stmrHeader.CHAIRED_BY)">
              <ion-icon name="chevron-down-outline"></ion-icon>
            </ion-button>
          </ion-col>
          <ion-col size="12" size-md="6" size-xl="4">
            <ion-label>{{ 'Operator' | translate }}:</ion-label>
            <ion-input [(ngModel)]="stmrHeader.OPERATOR" [disabled]="isSTMRReadonly(stmrHeader)" (ionChange)="onModelChange($event.detail.value)"></ion-input>
          </ion-col>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </ion-card>

  <!-- Topics Section -->
  <ion-header class="section-header">
  <ion-toolbar class="section-toolbar">
    <ion-title slot="start" class="section-title">{{ 'Topics' | translate }}</ion-title>
    <ion-buttons slot="end">
        <ion-button *ngIf="!isSTMRReadonly(stmrHeader)" fill="clear" (click)="addTopic()">
          <i class="fas fa-plus-circle" style="font-size: 30px;"></i>
        </ion-button>
      </ion-buttons>
  </ion-toolbar>
</ion-header>
  <ion-card>
    <ion-card-content>
      <ion-item *ngIf="stmrTopicEntity.length === 0 || getNoOfDeletedTopics(stmrTopicEntity) === stmrTopicEntity.length" (click)="addTopic()">
        {{ 'No Topics added.' | translate }}
      </ion-item>
      <ng-container *ngFor="let entity of stmrTopicEntity; let i = index">
        <ion-item *ngIf="entity.topic.P_MODE !== 'D'" (click)="editTopic(entity, i)">
          <ion-label>
            <h2>{{ entity.topic.TOPIC_NAME }} • <span class="small">{{ dispTime(entity.topic.TOPIC_START) }}</span></h2>
            <p>{{ entity.topic.TOPIC_NOTE }}</p>
          </ion-label>
          <ion-button fill="clear" color="danger" slot="end" (click)="removeTopic($event, i)" [disabled]="isSTMRReadonly(stmrHeader)">
            <ion-icon name="trash-outline"></ion-icon>
          </ion-button>
        </ion-item>
      </ng-container>
    </ion-card-content>
  </ion-card>

  <!-- Crew Section -->
  <ion-header class="section-header">
  <ion-toolbar class="section-toolbar">
    <ion-title slot="start" class="section-title">{{ 'Crew' | translate }}</ion-title>
    <ion-buttons slot="end">
      <ion-button *ngIf="!isSTMRReadonly(stmrHeader)" fill="clear" (click)="addCrew()">
        <i class="fa-regular fa-circle-user" style="font-size: 30px;"></i>
      </ion-button>
    </ion-buttons>
  </ion-toolbar>
</ion-header>
  <ion-card>
    <ion-card-content>
      <ion-item class="empty-state" *ngIf="getNoOfNonThirdPartyCrewMembers(stmrCrew) === 0" (click)="addCrew()">
        {{ 'No Crew members added.' | translate }}
      </ion-item>
      <ion-grid>
        <ion-row>
          <ng-container *ngFor="let crew of stmrCrew">
            <ion-col size="12" size-md="6" *ngIf="crew.CREW_TYPE !== 'THIRD_PARTY' && crew.P_MODE !== 'D'">
              <ion-item>
                <ion-label>
                  <h2>{{ crew.CREW_NAME }}</h2>
                  <p>{{ crew.CREW_POS }}</p>
                </ion-label>
                <!-- <ng-container *ngIf="crew.CREW_SIGN; else signButton">
                </ng-container> -->
                <ng-template #signButton>
                  <ion-button fill="outline" color="danger" slot="end" (click)="captureSign('CREW_SIGN', crew)">
                    {{ 'Tap to Add Signature' | translate }}
                  </ion-button>
                </ng-template>
                <ion-button fill="clear" slot="end" color="medium" (click)="removeCrewMember(crew)" *ngIf="!isSTMRReadonly(stmrHeader)">
                  <ion-icon name="close-outline"></ion-icon>
                </ion-button>
              </ion-item>
            </ion-col>
          </ng-container>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </ion-card>

  <!-- Third Party Crew Section -->
  <ion-header class="section-header">
    <ion-toolbar class="section-toolbar">
      <ion-title slot="start" class="section-title">{{ 'Third Party Crew' | translate }}</ion-title>
      <ion-buttons slot="end">
        <ion-button *ngIf="!isSTMRReadonly(stmrHeader)" fill="clear" (click)="addThirdPartyCrew()">
          <i class="fas fa-plus-circle" style="font-size: 30px;"></i>
        </ion-button>
      </ion-buttons>
    </ion-toolbar>
  </ion-header>
  <ion-card>
    <ion-card-content>
      <ion-item class="empty-state" *ngIf="getNoOfThirdPartyCrewMembers(stmrCrew) === 0" (click)="addThirdPartyCrew()">
        {{ 'No Third Party Crew members added.' | translate }}
      </ion-item>
      <ion-grid>
        <ion-row>
          <ng-container *ngFor="let crew of stmrCrew">
            <ion-col size="12" size-md="6" *ngIf="crew.CREW_TYPE === 'THIRD_PARTY' && crew.P_MODE !== 'D'">
              <ion-item>
                <ion-label>
                  <h2>{{ crew.CREW_NAME }}</h2>
                  <p>{{ crew.CREW_POS }}</p>
                </ion-label>
                <ng-container *ngIf="crew.CREW_SIGN; else thirdPartySignButton">
                  <ion-thumbnail slot="end" (click)="captureSign('CREW_SIGN', crew)">
                    <img [src]="crew.CREW_SIGN" alt="Signature" />
                  </ion-thumbnail>
                </ng-container>
                <ng-template #thirdPartySignButton>
                  <ion-button fill="outline" color="danger" slot="end" (click)="captureSign('CREW_SIGN', crew)">
                    {{ 'Tap to Add Signature' | translate }}
                  </ion-button>
                </ng-template>
                <ion-button fill="clear" slot="end" color="medium" (click)="removeCrewMember(crew)" *ngIf="!isSTMRReadonly(stmrHeader)">
                  <ion-icon name="close-outline"></ion-icon>
                </ion-button>
              </ion-item>
            </ion-col>
          </ng-container>
        </ion-row>
      </ion-grid>
    </ion-card-content>
  </ion-card>

  <!-- Crew Supervisor Section -->
  <ion-header class="section-header">
    <ion-toolbar class="section-toolbar">
      <ion-title slot="start" class="section-title">{{ 'Crew Supervisor' | translate }}</ion-title>
    </ion-toolbar>
  </ion-header>
  <ion-card>
    <ion-card-content>
      <ion-item class="empty-state" *ngIf="!stmrHeader.CREW_SUPERVISOR" (click)="addCrewSupervisor()">
        {{ 'No Crew Supervisor added.' | translate }}
      </ion-item>
      <ion-item *ngIf="stmrHeader.CREW_SUPERVISOR">
        <ion-label>
          <h2>{{ stmrHeader.CREW_SUPERVISOR }}</h2>
        </ion-label>
        <ion-button fill="outline" color="danger" slot="end" (click)="captureSign('CREW_SUPERVISOR_SIGN', stmrHeader)">
          {{ stmrHeader.CREW_SUPERVISOR_SIGN ? ('Signature Added' | translate) : ('Tap to Add Signature' | translate) }}
        </ion-button>
        <ion-button fill="clear" slot="end" color="medium" (click)="removeCrewSupervisor()" *ngIf="!isSTMRReadonly(stmrHeader)">
          <ion-icon name="close-outline"></ion-icon>
        </ion-button>
      </ion-item>
    </ion-card-content>
  </ion-card>
</ion-content>
