<ion-content class="vertical-align-content">
  <div class="center-container">
    <div class="overlay">
     <div class="custom-button-group">
  <ion-button
   class="login-button"
    expand="block"
    color="success"
    (click)="onSSOLoginButtonClicked()">
    <ion-label class="button-text">Login with SSO</ion-label>
  </ion-button>
  <div class="button-divider"></div>
  <ion-button
    class="server-selection-button" style="border-radius: 0%;"
    color="success"
    fill="solid"
    (click)="openModal()"
  >
    <i class="far fa-caret-square-up fpx-20" style="color: white"></i>
  </ion-button>
</div>


      <!-- Language Selector -->
      <div class="language-select-container">
        <ion-select label="Language:"
          interface="popover"
          [value]="utilityService.lang$ | async" style="color: white"
          (ionChange)="utilityService.changeLanguage($event.detail.value)">
          <ion-select-option value="en">English</ion-select-option>
          <ion-select-option value="es">Español</ion-select-option>
          <ion-select-option value="ar">العربية</ion-select-option>
        </ion-select>
      </div>

       <!-- <ion-button (click)="unviredLogin()"> Unvired Login</ion-button> -->
    </div>

    <!-- <ion-button (click)="forelectron()"> test</ion-button> -->

   
  </div>

  <ion-modal 
    #modal 
    [isOpen]="isModalOpen"
    [cssClass]="modalClass">
    <ng-template>
     <div >
        <ion-list>
          <ion-item *ngFor="let url of umpUrls" 
                    (click)="saveUrl(url.value)" 
                    button="true" 
                    >
                    
            <ion-label >
              <h2 style="color: gray;">{{url.name}}</h2>
              <p class="server-url">{{url.value}}</p>
            </ion-label>
          </ion-item>
        </ion-list>
      </div>
    </ng-template>
  </ion-modal>


 <p class="footer-right"  (dblclick)="unviredLogin()">&nbsp;{{'App Version'}}: {{version}}&nbsp;</p>
  
</ion-content>



