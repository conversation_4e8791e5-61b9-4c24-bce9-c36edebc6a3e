{"name": "PD-FORMS", "version": "0.0.1", "author": "Ionic Framework", "homepage": "https://ionicframework.com/", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^20.1.6", "@angular/common": "^20.1.6", "@angular/compiler": "^20.1.6", "@angular/core": "^20.1.6", "@angular/forms": "^20.1.6", "@angular/platform-browser": "^20.1.6", "@angular/platform-browser-dynamic": "^20.1.6", "@angular/router": "^20.1.6", "@awesome-cordova-plugins/deeplinks": "^8.0.2", "@awesome-cordova-plugins/device": "^8.0.2", "@awesome-cordova-plugins/file": "^8.0.2", "@awesome-cordova-plugins/in-app-browser": "^8.1.0", "@awesome-cordova-plugins/ionic-webview": "^8.1.0", "@awesome-cordova-plugins/splash-screen": "^8.0.2", "@awesome-cordova-plugins/status-bar": "^8.0.2", "@awesome-cordova-plugins/unvired-cordova-sdk": "^8.0.2", "@awesome-cordova-plugins/zip": "^8.0.2", "@fortawesome/fontawesome-free": "^6.7.2", "@ionic/angular": "^8.0.0", "@ionic/cli": "^7.2.1", "@ionic/cordova-builders": "^12.2.0", "@ngrx/effects": "^20.0.0", "@ngrx/entity": "^20.0.0", "@ngrx/store": "^20.0.0", "@ngrx/store-devtools": "^20.0.0", "@ngx-translate/core": "^16.0.4", "@ngx-translate/http-loader": "^16.0.1", "cordova": "^12.0.0", "fflate": "^0.8.2", "ionicons": "^7.4.0", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "rxjs": "^7.8.2", "tslib": "^2.3.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^20.1.5", "@angular-eslint/builder": "^19.0.0", "@angular-eslint/eslint-plugin": "^19.0.0", "@angular-eslint/eslint-plugin-template": "^19.0.0", "@angular-eslint/schematics": "^19.0.0", "@angular-eslint/template-parser": "^19.0.0", "@angular/cli": "^20.1.5", "@angular/compiler-cli": "^20.1.6", "@angular/language-service": "^20.1.6", "@ionic/angular-toolkit": "^12.0.0", "@types/jasmine": "~5.1.0", "@types/moment": "^2.11.29", "@types/node": "^24.2.1", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "cordova-android": "^14.0.1", "cordova-electron": "^4.0.0", "cordova-ios": "^7.1.1", "cordova-plugin-add-swift-support": "^2.0.2", "cordova-plugin-file": "github:srinidhirao/cordova-plugin-file", "cordova-plugin-firebase-messaging": "^8.0.1", "cordova-plugin-inappbrowser": "^6.0.0", "cordova-plugin-ionic-keyboard": "^2.0.5", "cordova-plugin-ionic-webview": "^5.0.0", "cordova-plugin-splashscreen": "5.0.2", "cordova-plugin-statusbar": "^2.4.2", "cordova-plugin-unvired-device": "^1.0.4", "cordova-plugin-unvired-electron-db": "^0.0.27", "cordova-plugin-unvired-logger": "^0.0.13", "cordova-plugin-unvired-universal-sdk": "^1.0.83", "eslint": "^9.16.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsdoc": "^48.2.1", "eslint-plugin-prefer-arrow": "1.2.2", "ionic-plugin-deeplinks": "^1.0.24", "ios": "^0.0.1", "jasmine-core": "~5.1.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.3"}, "description": "An Ionic project", "cordova": {"plugins": {"cordova-plugin-statusbar": {}, "cordova-plugin-splashscreen": {}, "cordova-plugin-ionic-webview": {}, "cordova-plugin-ionic-keyboard": {}, "ionic-plugin-deeplinks": {"URL_SCHEME": "forms", "DEEPLINK_SCHEME": "forms", "DEEPLINK_HOST": "callback"}, "cordova-plugin-add-swift-support": {}, "cordova-plugin-firebase-messaging": {"IOS_FIREBASE_POD_VERSION": "10.17.0"}, "cordova-plugin-unvired-universal-sdk": {}, "cordova-plugin-inappbrowser": {}, "cordova-plugin-file": {}}, "platforms": ["browser", "ios", "electron"], "buildOptions": {"electron": {"buildOptions": {"mac": {"identity": null, "hardenedRuntime": false, "gatekeeperAssess": false, "entitlements": null, "entitlementsInherit": null}}}}}, "volta": {"node": "22.16.0"}}